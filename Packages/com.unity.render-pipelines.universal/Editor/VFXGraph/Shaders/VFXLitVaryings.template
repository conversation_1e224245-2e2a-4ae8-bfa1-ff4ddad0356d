
#define URP_NEEDS_UVS (URP_USE_BASE_COLOR_MAP || URP_USE_MASK_MAP || USE_NORMAL_MAP || URP_USE_EMISSIVE_MAP)
#define URP_USE_EMISSIVE (URP_USE_EMISSIVE_MAP || URP_USE_EMISSIVE_COLOR || URP_USE_ADDITIONAL_EMISSIVE_COLOR || VFX_SIX_WAY_USE_ONE_EMISSIVE_CHANNEL)

${VFXInclude("Shaders/SixWay/SixWayVaryings.template"), VFX_MATERIAL_TYPE_SIX_WAY_SMOKE}

${VFXBegin:VFXURPLitVaryingsMacros}
#if (VFX_NEEDS_COLOR_INTERPOLATOR && URP_USE_BASE_COLOR) || URP_USE_ADDITIONAL_BASE_COLOR
#define VFX_VARYING_COLOR color.rgb
#define VFX_VARYING_ALPHA color.a
#endif

#ifndef VFX_MATERIAL_TYPE_SIX_WAY_SMOKE
    #define VFX_VARYING_SMOOTHNESS materialProperties.x
    #if URP_USE_EMISSIVE_MAP
        #define VFX_VARYING_EMISSIVESCALE materialProperties.w
    #endif
#endif

#if URP_USE_EMISSIVE_COLOR || URP_USE_ADDITIONAL_EMISSIVE_COLOR
#define VFX_VARYING_EMISSIVE emissiveColor.rgb
#endif

#if USE_EXPOSURE_WEIGHT
#define VFX_VARYING_EXPOSUREWEIGHT emissiveColor.a
#endif

#if URP_WORKFLOW_MODE_METALLIC
#define VFX_VARYING_METALLIC materialProperties.y
#elif URP_WORKFLOW_MODE_SPECULAR
#define VFX_VARYING_SPECULAR specularColor
#endif

#if USE_NORMAL_MAP
#define VFX_VARYING_NORMALSCALE materialProperties.z
#endif

#if VFX_MATERIAL_TYPE_SIX_WAY_SMOKE
    ${SixWayVaryingsMacros}
#endif

${VFXEnd}

${VFXBegin:VFXURPLitDeclareVaryings}

#if (VFX_NEEDS_COLOR_INTERPOLATOR && URP_USE_BASE_COLOR) || URP_USE_ADDITIONAL_BASE_COLOR
VFX_OPTIONAL_INTERPOLATION float4 color : COLOR0;
#endif
#if URP_WORKFLOW_MODE_SPECULAR
VFX_OPTIONAL_INTERPOLATION float3 specularColor : COLOR1;
#endif
#if URP_USE_EMISSIVE_COLOR || URP_USE_ADDITIONAL_EMISSIVE_COLOR
VFX_OPTIONAL_INTERPOLATION float4 emissiveColor : COLOR2;
#endif

#ifndef VFX_MATERIAL_TYPE_SIX_WAY_SMOKE
// x: smoothness
// y: metallic/thickness
// z: normal scale
// w: emissive scale
VFX_OPTIONAL_INTERPOLATION float4 materialProperties : TEXCOORD0;
#endif

#if VFX_MATERIAL_TYPE_SIX_WAY_SMOKE
${SixWayDeclareVaryings}
#endif

${VFXEnd}

${VFXBegin:VFXURPLitFillVaryings}
#ifndef VFX_SHADERGRAPH
#ifdef VFX_VARYING_SMOOTHNESS
${VFXLoadParameter:{smoothness}}
o.VFX_VARYING_SMOOTHNESS = smoothness;
#endif
#if URP_WORKFLOW_MODE_METALLIC
#ifdef VFX_VARYING_METALLIC
${VFXLoadParameter:{metallic}}
o.VFX_VARYING_METALLIC = metallic;
#endif
#elif URP_WORKFLOW_MODE_SPECULAR
#ifdef VFX_VARYING_SPECULAR
${VFXLoadParameter:{specularColor}}
o.VFX_VARYING_SPECULAR = specularColor.rgb;
#endif
#endif
#if USE_NORMAL_MAP
#ifdef VFX_VARYING_NORMALSCALE
${VFXLoadParameter:{normalScale}}
o.VFX_VARYING_NORMALSCALE = normalScale;
#endif
#endif
#if URP_USE_EMISSIVE_MAP
#ifdef VFX_VARYING_EMISSIVESCALE
${VFXLoadParameter:{emissiveScale}}
o.VFX_VARYING_EMISSIVESCALE = emissiveScale;
#endif
#endif
#ifdef VFX_VARYING_EMISSIVE
#if URP_USE_EMISSIVE_COLOR
o.VFX_VARYING_EMISSIVE = attributes.color;
#elif URP_USE_ADDITIONAL_EMISSIVE_COLOR
${VFXLoadParameter:{emissiveColor}}
o.VFX_VARYING_EMISSIVE = emissiveColor.rgb;
#endif
#endif
#if URP_USE_ADDITIONAL_BASE_COLOR
#ifdef VFX_VARYING_COLOR
${VFXLoadParameter:{baseColor}}
o.VFX_VARYING_COLOR = baseColor.rgb;
#endif
#endif

${SixWayFillVaryings}

#endif
${VFXEnd}
