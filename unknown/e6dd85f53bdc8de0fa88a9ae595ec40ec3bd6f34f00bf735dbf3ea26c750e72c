using System;
using System.IO;
using Cysharp.Threading.Tasks;
using OnePuz.Addressable;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.AddressableAssets;
using Object = UnityEngine.Object;

namespace OnePuz.Data
{
    [System.Serializable]
    public class ResourceAsset
    {
#if UNITY_EDITOR
        [OnValueChanged("UpdateResourcePath")] public Object Asset;
#endif

#if USE_ADDRESSABLES
        public AssetReference AssetReference;
#else
        public string ResourcePath;
#endif

#if UNITY_EDITOR
        [Button("Manual Update Resource Path")]
        public void UpdateResourcePath()
        {
            var assetPath = UnityEditor.AssetDatabase.GetAssetPath(Asset);
#if !USE_ADDRESSABLES
            var resourceIndex = assetPath.IndexOf(Path.Combine("Resources", ""), StringComparison.Ordinal);
            if (resourceIndex == -1)
            {
                OLogger.LogError($"[ResourceAsset] {Asset.name} is not in Resources folder");
                ResourcePath = string.Empty;
            }
            else
            {
                var nameIndex = assetPath.LastIndexOf(Asset.name, StringComparison.Ordinal);
                ResourcePath = Path.Combine(assetPath.Substring(resourceIndex + 10, nameIndex - resourceIndex - 10),
                    Path.GetFileNameWithoutExtension(assetPath));
            }
#else
            AssetReference = GetAssetReference(assetPath);
#endif

            UnityEditor.EditorUtility.SetDirty(Asset);
        }

        private AssetReference GetAssetReference(string assetPath)
        {
            var settings = UnityEditor.AddressableAssets.AddressableAssetSettingsDefaultObject.Settings;
            var guid = UnityEditor.AssetDatabase.AssetPathToGUID(assetPath);
            var entry = settings.FindAssetEntry(guid);
            if (entry != null) return new AssetReference(guid);

            entry = settings.CreateOrMoveEntry(guid, settings.DefaultGroup);
            entry.address = Path.GetFileNameWithoutExtension(assetPath);
            OLogger.Log($"There is no Addressable entry for {assetPath}, creating new one");
            return new AssetReference(guid);
        }
#endif

        private int _loadCount;

        public async UniTask<T> LoadAsync<T>() where T : Object
        {
#if USE_ADDRESSABLES
            if (AssetReference != null && AssetReference.RuntimeKeyIsValid())
            {
                _loadCount = 0;
                return await AddressableHelper.LoadAsync<T>(AssetReference);
                // await AssetReference.LoadAssetAsync<T>();
            }
            else if (_loadCount == 0)
            {
#if UNITY_EDITOR
                OLogger.LogError($"[ResourceAsset] Load failed: {Asset.name}, updating new path");
                UpdateResourcePath();
                _loadCount++;
                return await LoadAsync<T>();
#else
                OLogger.LogError($"[ResourceAsset] Load failed: {AssetReference.AssetGUID}");
                return null;
#endif
            }
#else
            var asset = Resources.Load<T>(ResourcePath);
            if (!asset && _loadCount == 0)
            {
#if UNITY_EDITOR
                OLogger.LogError($"[ResourceAsset] Load failed: {ResourcePath}, updating new path");
                UpdateResourcePath();
                _loadCount++;
                return await LoadAsync<T>();
#else
                OLogger.LogError($"[ResourceAsset] Load failed: {ResourcePath}");
                return null;
#endif
            }
            else
            {
                _loadCount = 0;
                return asset;
            }
#endif
            return null;
        }

        public void Unload()
        {
#if USE_ADDRESSABLES
            if (AssetReference != null && AssetReference.RuntimeKeyIsValid())
            {
                AddressableHelper.Unload(AssetReference);
            }
#else
            Resources.UnloadAsset(Asset);
#endif
        }
    }
}