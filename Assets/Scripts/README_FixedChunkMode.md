# Fixed Chunk Display Mode

## Tổng quan

Tính năng Fixed Chunk Display Mode được thêm vào để hỗ trợ các level chỉ có 1 chunk duy nhất, cho phép hiển thị toàn bộ chunk mà không phụ thuộc vào viewport hoặc camera movement.

## Tính năng mới

### 1. Always Show Full Chunk
- **Mục đích**: <PERSON>ôn hiển thị toàn bộ chunk thay vì chỉ một phần trong viewport
- **Sử dụng**: <PERSON><PERSON> hợp cho các level có kích thước cố định, không cần camera scrolling
- **Cài đặt**: `SandWorld.AlwaysShowFullChunk = true`

### 2. Use Fixed Canvas Size
- **Mục đích**: Canvas không bị scale theo camera orthographic size hoặc screen resolution
- **Sử dụng**: <PERSON><PERSON><PERSON> b<PERSON><PERSON> kích thước hiển thị nhất quán trên các thiết bị khác nhau
- **Cài đặt**: `SandWorld.UseFixedCanvasSize = true`

### 3. Fixed Canvas Scale
- **<PERSON><PERSON><PERSON> đích**: Cho phép set scale cố định cho canvas
- **Sử dụng**: Điều chỉnh kích thước hiển thị mà không ảnh hưởng đến logic game
- **Cài đặt**: `SandWorld.FixedCanvasScale = new Vector2(2f, 2f)`

## Cách sử dụng

### Trong Inspector (SandWorld component):

```csharp
[Header("Display Mode")]
public bool AlwaysShowFullChunk = false;
public bool UseFixedCanvasSize = false;
public Vector2 FixedCanvasScale = Vector2.one;
```

### Trong Code:

```csharp
// Bật fixed chunk mode
sandWorld.AlwaysShowFullChunk = true;
sandWorld.UseFixedCanvasSize = true;
sandWorld.FixedCanvasScale = new Vector2(2f, 2f);

// Áp dụng settings vào PixelWorld
if (sandWorld.PixelWorld != null)
{
    sandWorld.PixelWorld.AlwaysShowFullChunk = sandWorld.AlwaysShowFullChunk;
    sandWorld.PixelWorld.UseFixedCanvasSize = sandWorld.UseFixedCanvasSize;
    sandWorld.PixelWorld.FixedCanvasScale = sandWorld.FixedCanvasScale;
}
```

## Thay đổi trong hệ thống

### PixelWorld
- Viewport size = Chunk size khi `AlwaysShowFullChunk = true`
- Viewport position luôn là (0,0) trong fixed mode
- Camera movement không ảnh hưởng đến viewport trong fixed mode
- Coordinate conversion được cập nhật để hoạt động chính xác

### PixelCanvas
- Thêm method `SetFixedScale()` để set scale cố định
- `MatchCamera()` bị bypass khi sử dụng fixed scale

### Coordinate Conversion
- `ScreenToPixelPos()` và `PixelToScreenPos()` được cập nhật
- Thêm các helper methods cho fixed mode:
  - `ScreenToPixelPosFixed()`
  - `PixelToScreenPosFixed()`

## Test Scripts

### 1. PixelWorldFixedModeTest
- Kiểm tra các settings được áp dụng đúng
- Verify viewport dimensions và position
- Test canvas scaling

### 2. CoordinateConversionTest
- Test độ chính xác của coordinate conversion
- So sánh giữa normal mode và fixed mode
- Round-trip testing (Pixel->Screen->Pixel)

### 3. MouseInteractionDemo
- Demo tương tác chuột trong cả hai mode
- Hiển thị crosshair và pixel coordinates
- Cho phép vẽ pixels để test accuracy

## Backward Compatibility

Tất cả các thay đổi đều backward compatible:
- Default values giữ nguyên behavior cũ
- Existing code không cần thay đổi
- Chỉ khi explicitly enable các tính năng mới thì behavior mới được áp dụng

## Performance

- Fixed chunk mode có thể cải thiện performance vì:
  - Không cần tính toán viewport movement
  - Luôn chỉ load 1 chunk duy nhất
  - Ít job scheduling cho chunk loading/unloading

## Lưu ý

1. **Fixed chunk mode chỉ phù hợp cho single-chunk levels**
2. **Camera controls bị disable trong fixed mode** để tránh confusion
3. **Coordinate conversion accuracy** đã được test và verified
4. **Canvas scaling** hoạt động độc lập với camera settings
