using UnityEngine;
using Kamgam.SandGame;

namespace Demo
{
    /// <summary>
    /// Demo script to test mouse interaction accuracy in both normal and fixed chunk modes.
    /// Shows pixel coordinates under mouse cursor and allows drawing pixels.
    /// </summary>
    public class MouseInteractionDemo : MonoBehaviour
    {
        [Header("References")]
        public SandWorld sandWorld;
        public Camera mainCamera;
        
        [Header("Drawing Settings")]
        public PixelMaterialId drawMaterial = PixelMaterialId.Sand;
        public int brushSize = 3;
        public KeyCode drawKey = KeyCode.Mouse0;
        public KeyCode eraseKey = KeyCode.Mouse1;
        
        [Header("Mode Toggle")]
        public KeyCode toggleModeKey = KeyCode.Tab;
        
        private Vector3 currentMousePixelPos;
        private bool isFixedMode = false;
        private string statusText = "";

        void Start()
        {
            if (sandWorld == null)
                sandWorld = FindObjectOfType<SandWorld>();
                
            if (mainCamera == null)
                mainCamera = Camera.main;
        }

        void Update()
        {
            UpdateMousePosition();
            HandleInput();
            UpdateStatusText();
        }

        void UpdateMousePosition()
        {
            if (sandWorld?.PixelWorld == null)
                return;

            Vector3 mouseScreenPos = Input.mousePosition;
            currentMousePixelPos = sandWorld.PixelWorld.ScreenToPixelPos(mouseScreenPos, mainCamera);
        }

        void HandleInput()
        {
            // Toggle between normal and fixed mode
            if (Input.GetKeyDown(toggleModeKey))
            {
                ToggleMode();
            }
            
            // Drawing
            if (Input.GetKey(drawKey))
            {
                DrawAtMousePosition(drawMaterial);
            }
            else if (Input.GetKey(eraseKey))
            {
                DrawAtMousePosition(PixelMaterialId.Empty);
            }
        }

        void ToggleMode()
        {
            isFixedMode = !isFixedMode;
            
            if (sandWorld != null)
            {
                sandWorld.AlwaysShowFullChunk = isFixedMode;
                sandWorld.UseFixedCanvasSize = isFixedMode;
                sandWorld.FixedCanvasScale = Vector2.one * 2f; // 2x scale for better visibility
                
                if (sandWorld.PixelWorld != null)
                {
                    sandWorld.PixelWorld.AlwaysShowFullChunk = isFixedMode;
                    sandWorld.PixelWorld.UseFixedCanvasSize = isFixedMode;
                    sandWorld.PixelWorld.FixedCanvasScale = sandWorld.FixedCanvasScale;
                }
            }
            
            Debug.Log($"Switched to {(isFixedMode ? "Fixed Chunk" : "Normal")} mode");
        }

        void DrawAtMousePosition(PixelMaterialId material)
        {
            if (sandWorld?.PixelWorld == null || !sandWorld.PixelWorld.LoadSucceeded)
                return;

            var pixelWorld = sandWorld.PixelWorld;
            
            // Draw with brush size
            for (int dx = -brushSize/2; dx <= brushSize/2; dx++)
            {
                for (int dy = -brushSize/2; dy <= brushSize/2; dy++)
                {
                    float x = currentMousePixelPos.x + dx;
                    float y = currentMousePixelPos.y + dy;
                    
                    // Check bounds
                    if (isFixedMode)
                    {
                        if (x < 0 || x >= pixelWorld.ChunkWidth || 
                            y < 0 || y >= pixelWorld.ChunkHeight)
                            continue;
                    }
                    
                    // Draw pixel
                    pixelWorld.DrawPixelAt(x, y, material);
                }
            }
        }

        void UpdateStatusText()
        {
            if (sandWorld?.PixelWorld == null)
            {
                statusText = "PixelWorld not available";
                return;
            }

            var pw = sandWorld.PixelWorld;
            
            statusText = $"Mode: {(isFixedMode ? "Fixed Chunk" : "Normal")}\n";
            statusText += $"Mouse Pixel Pos: ({currentMousePixelPos.x:F1}, {currentMousePixelPos.y:F1})\n";
            statusText += $"Viewport: {pw.ViewportWidth}x{pw.ViewportHeight} at ({pw.ViewPortMinX:F0},{pw.ViewPortMinY:F0})\n";
            statusText += $"Chunk: {pw.ChunkWidth}x{pw.ChunkHeight}\n";
            
            if (isFixedMode)
            {
                statusText += $"Canvas Scale: {pw.FixedCanvasScale}\n";
            }
            else
            {
                statusText += $"Camera Size: {mainCamera.orthographicSize:F2}\n";
            }
            
            statusText += $"\nControls:\n";
            statusText += $"[{toggleModeKey}] Toggle Mode\n";
            statusText += $"[{drawKey}] Draw {drawMaterial}\n";
            statusText += $"[{eraseKey}] Erase\n";
            statusText += $"Brush Size: {brushSize}";
        }

        void OnGUI()
        {
            // Status panel
            var rect = new Rect(10, 10, 300, 200);
            GUI.Box(rect, "");
            
            var labelRect = new Rect(rect.x + 10, rect.y + 10, rect.width - 20, rect.height - 20);
            GUI.Label(labelRect, statusText);
            
            // Crosshair at mouse position
            if (sandWorld?.PixelWorld != null)
            {
                var screenPos = sandWorld.PixelWorld.PixelToScreenPos(currentMousePixelPos, mainCamera);
                
                // Draw crosshair
                var crosshairSize = 10f;
                var lineRect1 = new Rect(screenPos.x - crosshairSize/2, screenPos.y - 1, crosshairSize, 2);
                var lineRect2 = new Rect(screenPos.x - 1, screenPos.y - crosshairSize/2, 2, crosshairSize);
                
                GUI.color = Color.red;
                GUI.DrawTexture(lineRect1, Texture2D.whiteTexture);
                GUI.DrawTexture(lineRect2, Texture2D.whiteTexture);
                GUI.color = Color.white;
                
                // Show pixel coordinates near cursor
                var coordRect = new Rect(screenPos.x + 15, screenPos.y - 10, 100, 20);
                GUI.Label(coordRect, $"({currentMousePixelPos.x:F0},{currentMousePixelPos.y:F0})");
            }
        }
    }
}
