using UnityEngine;
using Kamgam.SandGame;

namespace Tests
{
    /// <summary>
    /// Test script to verify the new fixed chunk display mode functionality.
    /// Attach this to a GameObject in the scene to test the implementation.
    /// </summary>
    public class PixelWorldFixedModeTest : MonoBehaviour
    {
        [Header("Test Settings")]
        public SandWorld sandWorld;
        public bool testAlwaysShowFullChunk = true;
        public bool testUseFixedCanvasSize = true;
        public Vector2 testFixedCanvasScale = new Vector2(2f, 2f);
        
        [Header("Test Results")]
        public bool testPassed = false;
        public string testMessage = "";

        void Start()
        {
            if (sandWorld == null)
            {
                sandWorld = FindObjectOfType<SandWorld>();
            }

            if (sandWorld == null)
            {
                testMessage = "SandWorld not found in scene";
                testPassed = false;
                return;
            }

            RunTests();
        }

        void RunTests()
        {
            testMessage = "Running tests...";
            
            // Test 1: Verify settings are applied correctly
            sandWorld.AlwaysShowFullChunk = testAlwaysShowFullChunk;
            sandWorld.UseFixedCanvasSize = testUseFixedCanvasSize;
            sandWorld.FixedCanvasScale = testFixedCanvasScale;

            // Wait for next frame to ensure settings are applied
            StartCoroutine(VerifySettingsAfterFrame());
        }

        System.Collections.IEnumerator VerifySettingsAfterFrame()
        {
            yield return null; // Wait one frame

            if (sandWorld.PixelWorld == null)
            {
                testMessage = "PixelWorld not initialized";
                testPassed = false;
                yield break;
            }

            // Test 2: Verify PixelWorld has correct settings
            bool settingsCorrect = 
                sandWorld.PixelWorld.AlwaysShowFullChunk == testAlwaysShowFullChunk &&
                sandWorld.PixelWorld.UseFixedCanvasSize == testUseFixedCanvasSize &&
                sandWorld.PixelWorld.FixedCanvasScale == testFixedCanvasScale;

            if (!settingsCorrect)
            {
                testMessage = "Settings not applied correctly to PixelWorld";
                testPassed = false;
                yield break;
            }

            // Test 3: Verify viewport dimensions in AlwaysShowFullChunk mode
            if (testAlwaysShowFullChunk)
            {
                bool viewportCorrect = 
                    sandWorld.PixelWorld.ViewportWidth == sandWorld.PixelWorld.ChunkWidth &&
                    sandWorld.PixelWorld.ViewportHeight == sandWorld.PixelWorld.ChunkHeight;

                if (!viewportCorrect)
                {
                    testMessage = $"Viewport size incorrect. Expected: {sandWorld.PixelWorld.ChunkWidth}x{sandWorld.PixelWorld.ChunkHeight}, " +
                                 $"Got: {sandWorld.PixelWorld.ViewportWidth}x{sandWorld.PixelWorld.ViewportHeight}";
                    testPassed = false;
                    yield break;
                }
            }

            // Test 4: Verify viewport position in AlwaysShowFullChunk mode
            if (testAlwaysShowFullChunk)
            {
                bool viewportPositionCorrect = 
                    sandWorld.PixelWorld.ViewPortMinX == 0 &&
                    sandWorld.PixelWorld.ViewPortMinY == 0;

                if (!viewportPositionCorrect)
                {
                    testMessage = $"Viewport position incorrect. Expected: (0,0), " +
                                 $"Got: ({sandWorld.PixelWorld.ViewPortMinX},{sandWorld.PixelWorld.ViewPortMinY})";
                    testPassed = false;
                    yield break;
                }
            }

            // Test 5: Verify canvas scale in fixed mode
            if (testUseFixedCanvasSize && sandWorld.PixelCanvas != null)
            {
                var canvasScale = sandWorld.PixelCanvas.RenderTarget.transform.localScale;
                bool scaleCorrect = 
                    Mathf.Approximately(canvasScale.x, testFixedCanvasScale.x) &&
                    Mathf.Approximately(canvasScale.y, testFixedCanvasScale.y);

                if (!scaleCorrect)
                {
                    testMessage = $"Canvas scale incorrect. Expected: {testFixedCanvasScale}, " +
                                 $"Got: ({canvasScale.x},{canvasScale.y})";
                    testPassed = false;
                    yield break;
                }
            }

            // All tests passed
            testMessage = "All tests passed successfully!";
            testPassed = true;
            
            Debug.Log($"PixelWorldFixedModeTest: {testMessage}");
        }

        void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 200));
            GUILayout.Label("PixelWorld Fixed Mode Test", GUI.skin.box);
            GUILayout.Label($"Test Status: {(testPassed ? "PASSED" : "FAILED")}");
            GUILayout.Label($"Message: {testMessage}");
            
            if (GUILayout.Button("Run Tests Again"))
            {
                RunTests();
            }
            
            GUILayout.EndArea();
        }
    }
}
