using UnityEngine;
using Kamgam.SandGame;

namespace Tests
{
    /// <summary>
    /// Test script to verify coordinate conversion accuracy in both normal and fixed chunk modes.
    /// </summary>
    public class CoordinateConversionTest : MonoBehaviour
    {
        [Header("Test Settings")]
        public SandWorld sandWorld;
        public Camera testCamera;
        public bool runTestsOnStart = true;
        
        [Header("Test Points")]
        public Vector3[] testPixelPositions = new Vector3[]
        {
            new Vector3(0, 0, 0),           // Bottom-left corner
            new Vector3(160, 90, 0),        // Center (assuming 320x180 chunk)
            new Vector3(319, 179, 0),       // Top-right corner
            new Vector3(50, 50, 0),         // Random point 1
            new Vector3(250, 130, 0)        // Random point 2
        };
        
        [Header("Test Results")]
        public bool allTestsPassed = false;
        public string testResults = "";
        
        private bool testsCompleted = false;

        void Start()
        {
            if (sandWorld == null)
                sandWorld = FindObjectOfType<SandWorld>();
                
            if (testCamera == null)
                testCamera = Camera.main;

            if (runTestsOnStart)
            {
                // Wait a frame for initialization
                Invoke(nameof(RunCoordinateTests), 0.1f);
            }
        }

        public void RunCoordinateTests()
        {
            if (sandWorld?.PixelWorld == null)
            {
                testResults = "PixelWorld not available for testing";
                return;
            }

            testResults = "Running coordinate conversion tests...\n";
            allTestsPassed = true;
            
            // Test in normal mode
            TestNormalMode();
            
            // Test in fixed chunk mode
            TestFixedChunkMode();
            
            testsCompleted = true;
            
            if (allTestsPassed)
            {
                testResults += "\n✓ All coordinate conversion tests PASSED!";
                Debug.Log("CoordinateConversionTest: All tests passed!");
            }
            else
            {
                testResults += "\n✗ Some coordinate conversion tests FAILED!";
                Debug.LogWarning("CoordinateConversionTest: Some tests failed!");
            }
        }

        void TestNormalMode()
        {
            testResults += "\n--- Testing Normal Mode ---\n";
            
            // Set to normal mode
            sandWorld.AlwaysShowFullChunk = false;
            sandWorld.UseFixedCanvasSize = false;
            
            if (sandWorld.PixelWorld != null)
            {
                sandWorld.PixelWorld.AlwaysShowFullChunk = false;
                sandWorld.PixelWorld.UseFixedCanvasSize = false;
            }
            
            // Test coordinate conversions
            TestCoordinateConversions("Normal Mode");
        }

        void TestFixedChunkMode()
        {
            testResults += "\n--- Testing Fixed Chunk Mode ---\n";
            
            // Set to fixed chunk mode
            sandWorld.AlwaysShowFullChunk = true;
            sandWorld.UseFixedCanvasSize = true;
            sandWorld.FixedCanvasScale = Vector2.one;
            
            if (sandWorld.PixelWorld != null)
            {
                sandWorld.PixelWorld.AlwaysShowFullChunk = true;
                sandWorld.PixelWorld.UseFixedCanvasSize = true;
                sandWorld.PixelWorld.FixedCanvasScale = Vector2.one;
            }
            
            // Test coordinate conversions
            TestCoordinateConversions("Fixed Chunk Mode");
        }

        void TestCoordinateConversions(string modeName)
        {
            var pixelWorld = sandWorld.PixelWorld;
            
            foreach (var pixelPos in testPixelPositions)
            {
                // Skip positions outside chunk bounds in fixed mode
                if (sandWorld.AlwaysShowFullChunk)
                {
                    if (pixelPos.x < 0 || pixelPos.x >= pixelWorld.ChunkWidth ||
                        pixelPos.y < 0 || pixelPos.y >= pixelWorld.ChunkHeight)
                    {
                        continue;
                    }
                }
                
                // Test PixelToScreenPos -> ScreenToPixelPos round trip
                var screenPos = pixelWorld.PixelToScreenPos(pixelPos, testCamera);
                var backToPixel = pixelWorld.ScreenToPixelPos(screenPos, testCamera);
                
                float pixelError = Vector3.Distance(pixelPos, backToPixel);
                bool pixelTestPassed = pixelError < 1.0f; // Allow 1 pixel tolerance
                
                if (!pixelTestPassed)
                {
                    allTestsPassed = false;
                    testResults += $"✗ {modeName} Pixel->Screen->Pixel failed for {pixelPos}: " +
                                  $"got {backToPixel}, error: {pixelError:F2}\n";
                }
                else
                {
                    testResults += $"✓ {modeName} Pixel->Screen->Pixel OK for {pixelPos}\n";
                }
                
                // Test WorldToPixelPos -> PixelToWorldPos round trip
                var worldPos = pixelWorld.PixelToWorldPos(pixelPos);
                var backToPixelFromWorld = pixelWorld.WorldToPixelPos(worldPos);
                
                float worldError = Vector3.Distance(pixelPos, backToPixelFromWorld);
                bool worldTestPassed = worldError < 0.1f; // Very small tolerance for world conversion
                
                if (!worldTestPassed)
                {
                    allTestsPassed = false;
                    testResults += $"✗ {modeName} Pixel->World->Pixel failed for {pixelPos}: " +
                                  $"got {backToPixelFromWorld}, error: {worldError:F2}\n";
                }
                else
                {
                    testResults += $"✓ {modeName} Pixel->World->Pixel OK for {pixelPos}\n";
                }
            }
        }

        void OnGUI()
        {
            if (!testsCompleted)
                return;
                
            var rect = new Rect(10, 10, Screen.width - 20, Screen.height - 20);
            GUILayout.BeginArea(rect);
            
            GUILayout.Label("Coordinate Conversion Test Results", GUI.skin.box);
            
            var scrollViewRect = new Rect(0, 30, rect.width, rect.height - 80);
            GUILayout.BeginArea(scrollViewRect);
            
            var style = new GUIStyle(GUI.skin.label);
            style.wordWrap = true;
            style.fontSize = 10;
            
            GUILayout.Label(testResults, style);
            
            GUILayout.EndArea();
            
            GUILayout.BeginArea(new Rect(0, rect.height - 50, rect.width, 40));
            
            if (GUILayout.Button("Run Tests Again"))
            {
                testsCompleted = false;
                RunCoordinateTests();
            }
            
            GUILayout.EndArea();
            GUILayout.EndArea();
        }
    }
}
