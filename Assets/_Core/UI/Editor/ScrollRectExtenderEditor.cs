using _FeatureHub.UI.Runtime;
using UnityEditor;
using UnityEditor.UI;
using UnityEngine;

namespace _FeatureHub.UI.Editor
{
    [CustomEditor(typeof(ScrollRectExtender), true)]
    [CanEditMultipleObjects]
    public class ScrollRectExtenderEditor : ScrollRectEditor
    {
        private GUIStyle m_TitleStyle;
        private SerializedProperty m_IsDraggable;
        
        protected override void OnEnable()
        {
            base.OnEnable();
            m_TitleStyle = new GUIStyle(EditorStyles.boldLabel) { richText = true, alignment = TextAnchor.MiddleCenter};
            m_IsDraggable = serializedObject.FindProperty("isDraggable");
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            EditorGUILayout.Separator();
            GUILayout.Label("<color=grey>────────────────────[EXTENT REGION]────────────────────</color>", m_TitleStyle);
            EditorGUILayout.Separator();
            EditorGUILayout.PropertyField(m_IsDraggable);
            serializedObject.ApplyModifiedProperties();
        }
    }
}