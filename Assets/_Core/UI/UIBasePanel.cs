using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Audio;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public enum PanelLayer
    {
        DEFAULT = 0,
        UI = 500,
        POPUP = 600,
        OVERLAY = 700
    }

    public enum PanelState
    {
        CLOSED,
        CLOSING,
        OPENED,
        OPENING,
    }

    public interface IPanel
    {
        bool pIsPopup { get; }
        bool pIsOpen { get; }

        PanelState pState { get; }

        GameObject pGameObject { get; }
        CanvasGroup pCanvasGroup { get; }
        Canvas pCanvas { get; }

        string pId { get; }

        PanelArgs pArgs { get; set; }

        void Init(string id);
        
        void RegisterCancellation(CancellationToken cancellationToken);

        UniTask ShowAsync(PanelArgs options);
        UniTask CloseAsync(bool instant = false);

        void Show(PanelArgs options);
        void Close();
    }

    public interface IRelativePanel
    {
        IPanel pManagerPanel { get; }
    }
    
    public interface IAttachedScenePanel
    {
        string pAttachedSceneId { get; }
    }

    [RequireComponent(typeof(Canvas))]
    [RequireComponent(typeof(GraphicRaycaster))]
    public abstract class UIBasePanel : MonoBehaviour, IPanel
    {
        public Canvas pCanvas { get; private set; }
        protected GraphicRaycaster pRaycaster { get; private set; }
        public CanvasGroup pCanvasGroup { get; private set; }

        public string pId { get; private set; }
        
        public PanelArgs pArgs { get; set; }
        public bool pIsPopup => pArgs != null && pArgs.mode != ShowMode.PERSISTENT;
        
        public bool pIsOpen => pState == PanelState.OPENED || pState == PanelState.OPENING;
        
        public PanelState pState { get; private set; } = PanelState.CLOSED;

        private GameObject _gameObject;
        public GameObject pGameObject => _gameObject ??= gameObject;

        protected RectTransform _container;
        
        protected CancellationToken _cancellationToken;

        public virtual void Init(string id)
        {
            pId = id;

            pCanvas = GetComponent<Canvas>();
            pRaycaster = GetComponent<GraphicRaycaster>();

            _container = transform.Find("Container").GetComponent<RectTransform>();
            pCanvasGroup = _container.GetComponent<CanvasGroup>();
            pCanvasGroup.alpha = 1;

            pCanvas.enabled = false;
            pRaycaster.enabled = false;
            
            _container.gameObject.SetActive(false);
            
            SetupCanvasScaler();
            
            pState = PanelState.CLOSED;
        }

        private void SetupCanvasScaler()
        {
            var canvasScaler = GetComponent<CanvasScaler>();
            var aspectRatio = Screen.height / (float)Screen.width;
            
            canvasScaler.matchWidthOrHeight = aspectRatio switch
            {
                > 1.8f => 0, // 18:9 ratio
                <= 1.5f => 1, // Tablet ratio
                _ => 0 // 16:9 or 16:10 ratio
            };
        }
        
        public void RegisterCancellation(CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
        }

        public async UniTask ShowAsync(PanelArgs args)
        {
            pArgs = args;

            if (pIsOpen)
            {
                // OnBeforeFocus();
                // OnAfterFocus();
                return;
            }

            pCanvas.enabled = true;
            pRaycaster.enabled = true;
            pCanvasGroup.interactable = false;
            pCanvasGroup.blocksRaycasts = true;
            _container.gameObject.SetActive(true);

            pState = PanelState.OPENING;
            
            OnBeforeFocus();
            
            await TransitionInAsync(_cancellationToken);

            pCanvasGroup.interactable = true;
            
            OnAfterFocus();

            pState = PanelState.OPENED;
        }

        public async UniTask CloseAsync(bool instant = false)
        {
            if (!pIsOpen) return;

            pState = PanelState.CLOSING;
            
            pCanvasGroup.blocksRaycasts = true;
            pCanvasGroup.interactable = false;
            
            OnBeforeLostFocus();

            if (!instant)
            {
                await TransitionOutAsync(_cancellationToken);
            }

            OnAfterLostFocus();
            
            pRaycaster.enabled = false;
            pCanvas.enabled = false;
            pCanvasGroup.blocksRaycasts = false;
            _container.gameObject.SetActive(false);

            pState = PanelState.CLOSED;
        }
        
        protected virtual void OnDestroy()
        {
            if (pArgs != null)
            {
                pArgs.Release();
                pArgs = null;
            }
        }

        protected abstract UniTask TransitionInAsync(CancellationToken cancellationToken);
        protected abstract UniTask TransitionOutAsync(CancellationToken cancellationToken);

        protected virtual void OnBeforeFocus() { }
        protected virtual void OnAfterFocus() { }
        protected virtual void OnBeforeLostFocus() { }
        protected virtual void OnAfterLostFocus() { }

        #region Shorthands
        public void Show(PanelArgs args = null)
        {
            UIShortcut.Instance.Show(pId, args);
        }

        public void Close()
        {
            UIShortcut.Instance.Close(pId);
        }
        #endregion
    }

}