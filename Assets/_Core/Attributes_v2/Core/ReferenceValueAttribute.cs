using System;
using System.Reflection;
using _FeatureHub.Attributes.Definition;
using _FeatureHub.Attributes.Extensions;
using UnityEngine;
using Object = UnityEngine.Object;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
#endif

namespace _FeatureHub.Attributes.Core
{
    [AttributeUsage(AttributeTargets.Field, AllowMultiple = true)]
    public class ReferenceValueAttribute : PropertyAttribute
    {
        public bool manually { get; set; }
        
        public bool defaultReference { get; set; }
        
        public bool inlineEditor { get; set; }
        
        // public bool expanded { get; set; }
        
        public readonly string relativePath;
        
        public readonly (string name, SpecifiedType type) specifiedTarget;

        #region ──────────────────────────────[Contructor]──────────────────────────────

        /// <summary>
        /// Reference from components of this serialized object.
        /// </summary>
        /// <param name="manually">This field is not reference in background, but operate manually on the inspector.</param>
        /// <param name="inlineEditor">Inline of this field is displayable.</param>
        /// <param name="defaultReference">Value of this field is referenced in editor</param>
        public ReferenceValueAttribute(bool manually = false, bool inlineEditor = false, /*bool expanded = false,*/ bool defaultReference = false)
        {
            this.manually = manually;
            this.defaultReference = defaultReference;
            this.inlineEditor = inlineEditor;
            // this.expanded = expanded;
            relativePath = string.Empty;
            specifiedTarget = default((string, SpecifiedType));
        }

        /// <summary>
        /// Supports a variety of path formats, including name of field with prefix "@" (e.g. "@m_FieldName/..etc."),
        /// name of the local object is a child of root containing itself with prefix "local::" (e.g. "local::objectName/..etc."),
        /// name of the global object is an instance in hierarchy with prefix "global::" (e.g. "global::objectName/..etc.).
        /// </summary>
        /// <param name="relativePath">Relative path to find target (p.s. Ignorable name of intermediate objects).</param>
        /// <param name="manually">This field is not reference in background, but operate manually on the inspector.</param>
        /// <param name="inlineEditor">Inline of this field is displayable.</param>
        /// <param name="defaultReference">Value of this field is referenced in editor</param>
        public ReferenceValueAttribute(string relativePath, bool manually = false, bool inlineEditor = false, /* bool expanded = false,*/ bool defaultReference = false)
        {
            this.manually = manually;
            this.defaultReference = defaultReference;
            this.inlineEditor = inlineEditor;
            var entry = relativePath.Split('/')[0];

            if (entry.Contains("@"))
            {
                specifiedTarget.name = entry.Replace("@", "");
                specifiedTarget.type = SpecifiedType.Field;
                relativePath = relativePath.Contains($"{entry}/") ? relativePath.Replace($"{entry}/", "") : relativePath.Replace($"{entry}", "");
            }
            else if (entry.Contains("local::"))
            {
                specifiedTarget.name = entry.Replace("local::", "");
                specifiedTarget.type = SpecifiedType.Local;
                relativePath = relativePath.Contains($"{entry}/") ? relativePath.Replace($"{entry}/", "") : relativePath.Replace($"{entry}", "");
            }
            else if (entry.Contains("global::"))
            {
                specifiedTarget.name = entry.Replace("global::", "");
                specifiedTarget.type = SpecifiedType.Global;
                relativePath = relativePath.Contains($"{entry}/") ? relativePath.Replace($"{entry}/", "") : relativePath.Replace($"{entry}", "");
            }
            else if (entry.Contains("res::"))
            {
                specifiedTarget.name = string.Empty;
                specifiedTarget.type = SpecifiedType.Resources;
                relativePath = relativePath.TrimStart("res::".ToCharArray());
            }
            else if (entry.Contains("adb::"))
            {
                specifiedTarget.name = string.Empty;
                specifiedTarget.type = SpecifiedType.AssetDatabase;
                relativePath = relativePath.TrimStart("adb::".ToCharArray());
            }

            this.relativePath = relativePath;
        }

        #endregion

#if UNITY_EDITOR
        private static Object GetDefaultValue(Object target, FieldInfo info)
        {
            var scripts = Array.FindAll(Resources.FindObjectsOfTypeAll(typeof(MonoScript)),
                arg => string.CompareOrdinal(arg.name, target.GetType().Name) == 0);

            var result = default(Object);
            if (scripts is { Length: > 0 })
            {
                foreach (var script in scripts)
                {
                    var monoImporter = (MonoImporter)AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(script));
                    var defaultValue = monoImporter?.GetDefaultReference(info.Name);
                    if (defaultValue is null)
                        continue;
                    result = defaultValue;
                    break;
                }
            }
            
            return result;
        }

        public Object GetAssignedValue(Component target, FieldInfo info)
        {
            if (defaultReference)
                return GetDefaultValue(target, info);
            switch (specifiedTarget.type)
            {
                case SpecifiedType.Resources:
                    return Resources.Load(relativePath, info.FieldType);
                case SpecifiedType.AssetDatabase:
                    return AssetDatabase.LoadAssetAtPath(relativePath, info.FieldType);
            }
            
            var specifiedTransform = GetSpecifiedTarget(target);
            return string.IsNullOrEmpty(relativePath)
                ? specifiedTransform?.GetObject(info.FieldType)
                : specifiedTransform?.FindByPath(relativePath, info.FieldType);
        }

        public Transform GetSpecifiedTarget(Component target)
        {
            switch (specifiedTarget.type)
            {
                case SpecifiedType.None:
                    return target.transform;
                case SpecifiedType.Field:
                    var fieldInfo = default(FieldInfo);
                    var currentType = target.GetType();
                    while (currentType != null && currentType.IsSubclassOf(typeof(MonoBehaviour)))
                    {
                        fieldInfo = currentType.GetField(specifiedTarget.name, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                        if(fieldInfo != null)
                            break;
                        currentType = currentType.BaseType;
                    }
                    
                    Debug.Assert(fieldInfo != null, $"{nameof(fieldInfo)} ({specifiedTarget.name}) is null");
                    var referenceValue = fieldInfo.GetValue(target);
                    if(referenceValue == null) return null;
                    if (typeof(GameObject).IsAssignableFrom(fieldInfo.FieldType))
                        return ((GameObject)referenceValue).transform;
                    if (typeof(Component).IsAssignableFrom(fieldInfo.FieldType))
                        return ((Component)referenceValue).transform;
                    return null;
                case SpecifiedType.Local:
                    if (PrefabUtility.IsPartOfPrefabAsset(target.gameObject) || PrefabStageUtility.GetPrefabStage(target.gameObject) is not null)
                        return null;
                    return string.CompareOrdinal(target.transform.root.name, specifiedTarget.name) == 0
                        ? target.transform.root
                        : target.transform.root.FindDeep(specifiedTarget.name);
                case SpecifiedType.Global:
                    if (PrefabUtility.IsPartOfPrefabAsset(target.gameObject) || PrefabStageUtility.GetPrefabStage(target.gameObject) is not null)
                        return null;
                    return target.FindDeepInHierarchy(specifiedTarget.name);
                default:
                    throw new ArgumentOutOfRangeException(nameof(specifiedTarget.type), specifiedTarget.type, null);
            }
        }
#endif
    }
}