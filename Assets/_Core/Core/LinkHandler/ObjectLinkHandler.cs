// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;

namespace OnePuz.Handlers
{
	public struct ObjectLinkHandler<T> : ILinkHandler
	{
		private readonly WeakReference objectRef;

		public ObjectLinkHandler(T obj)
		{
			this.objectRef = new WeakReference(obj);
		}

		public bool IsActive => !IsDestroyed;
		public bool IsDestroyed => !objectRef.IsAlive || ((T)objectRef.Target) == null;
	}
}
