using System;
using System.Collections;
using OnePuz.Shared;
using UnityEngine;
using UnityEngine.Events;

namespace OnePuz.OPTimeline
{
    /// <summary>
    /// The Animator Play State
    /// </summary>
    public enum OPAnimatorPlayState
    {
        None,
        Playing,
        Paused,
        Finished
    }
    
    /// <summary>
    /// The Animator Events
    /// </summary>
    [System.Serializable]
    public class OPAnimatorEvents
    {
        public UnityEvent onPlay;
        public UnityEvent onPause;
        public UnityEvent onResume;
        public UnityEvent onStop;
        public UnityEvent onComplete;
        public UnityEvent onOneLoopCompleted;
    }
    
    /// <summary>
    /// The Main Animator Player Class
    /// Inherits from OPAnimator
    /// Here you can Play and Control the Animator like Play, Pause, Resume, Stop, Restart
    /// </summary>
    [OMTitle("OP Animator Player")]
    public class OPAnimatorPlayer : OPAnimator
    {
        [SerializeField] private bool loop = false;
        [SerializeField] private OPAnimatorEvents events;
        
        public OPAnimatorPlayState PlayState { get; protected set; } = OPAnimatorPlayState.None;

        private Action _completeCallback;

        public bool Loop { get => loop; set => loop = value; }

        private void OnEnable()
        {
            if (PlayOnEnable)
            {
                Play();
            }
        }

        private void Update()
        {
            if(PlayState != OPAnimatorPlayState.Playing) return;
            Simulate(false);

            if (TimelineTime >= FullDuration)
            {
                if (loop)
                {
                    events.onOneLoopCompleted?.Invoke();
                    
                    Restart();
                    return;
                }
                OnComplete();
            }
        }

        /// <summary>
        /// Restart the Animation from the beginning
        /// </summary>
        public void Restart()
        {
            foreach (var clip in GetClips())
            {
                clip.OnTimelineCompleted();
            }
            TimelineTime = 0f;
            PlayState = OPAnimatorPlayState.Playing;
            foreach (var clip in GetClips())
            {
                clip.OnTimelineStarted();
                clip.Reset();
            }
        }

        /// <summary>
        /// Reset the Animation from the beginning
        /// </summary>
        public void Reset()
        {
            TimelineTime = 0f;
            foreach (var clip in GetClips())
            {
                clip.OnTimelineStarted();
                clip.Reset();
            }
        }

        /// <summary>
        /// Play the Animation from the beginning
        /// </summary>
        public void Play()
        {
            Play(null);
        }

        public IEnumerator PlayAsync()
        {
            Play();
            while (PlayState != OPAnimatorPlayState.Finished)
            {
                yield return null;
            }
        }

        /// <summary>
        /// Play the Animation from the beginning with a callback when the animation is finished
        /// </summary>
        /// <param name="completeCallback">finish callback</param>
        public void Play(Action completeCallback)
        {
            TimelineTime = 0f;
            _completeCallback = completeCallback;
            PlayState = OPAnimatorPlayState.Playing;
            foreach (var clip in GetClips())
            {
                clip.OnTimelineStarted();
                clip.OnAnimatorStartPlaying();
            }
            Evaluate(0,false);
            events.onPlay?.Invoke();
        }
        
        /// <summary>
        /// Stop The Animation
        /// </summary>
        public void Stop()
        {
            PlayState = OPAnimatorPlayState.Finished;
            TimelineTime = 0;
            events.onStop?.Invoke();
        }
        
        /// <summary>
        /// Pause the Animation
        /// </summary>
        public void Pause()
        {
            PlayState = OPAnimatorPlayState.Paused;
            events.onPause?.Invoke();
        }
        
        /// <summary>
        /// Resume the Animation if it is paused
        /// </summary>
        public void Resume()
        {
            PlayState = OPAnimatorPlayState.Playing;
            events.onResume?.Invoke();
        }
        
        /// <summary>
        /// this method is called when the animation is finished
        /// </summary>
        private void OnComplete()
        {
            PlayState = OPAnimatorPlayState.Finished;
            _completeCallback?.Invoke();
            _completeCallback = null;
            events.onComplete?.Invoke();
            
            //Complete all clips
            foreach (var clip in GetClips())
            {
                clip.OnTimelineCompleted();
                clip.OnAnimatorCompletePlaying();
            }
        }
        
        public IEnumerator WaitForComplete()
        {
            while (PlayState != OPAnimatorPlayState.Finished)
            {
                yield return null;
            }
        }
    }
}