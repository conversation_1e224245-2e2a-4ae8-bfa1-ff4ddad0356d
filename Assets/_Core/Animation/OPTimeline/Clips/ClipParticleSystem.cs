using OnePuz.AnimationSequencer;
using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Particle/Particle System", "Particle System")]
    public class ClipParticleSystem : OPClip
    {
        [SerializeField, CheckForNull] private ParticleSystem _target;

        private bool _onPreviewMode = false;

        protected override void OnStateChanged(OPEvaluateState currentState, OPEvaluateState lastState) { }

        protected override void OnEnter()
        {
            if (_onPreviewMode)
                _target.PlayInEditor();
            else
                _target.Play();
        }

        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if (state != OPEvaluateState.Running) return;
            if (_target == null) return;
        }

        protected override void OnExit()
        {
            if (_target.isPlaying && !_target.main.loop)
            {
                _target.Stop();
            }
        }

        public override void OnTimelineStarted()
        {
            _target.Stop();
        }

        public override void OnTimelineCompleted()
        {
            _target.Stop();
        }

        public override void OnPreviewModeChanged(bool previewMode)
        {
            if (!IsValid()) return;

            _target.Stop();

            _onPreviewMode = previewMode;
        }

        public override void OnClipAddedToOPAnimator(OPAnimator animator)
        {
            
        }

        public override void OnAnimatorStartPlaying()
        {
        }

        public override void OnAnimatorCompletePlaying()
        {
            _target.Stop();
        }

        public override bool CanBePlayedInPreviewMode()
        {
            return true;
        }

        public override bool IsValid()
        {
            return _target != null;
        }

        public override Component GetTarget()
        {
            return _target;
        }

        public override void SetTarget(GameObject newTarget)
        {
            this._target = newTarget.GetComponent<ParticleSystem>();
        }
    }
}