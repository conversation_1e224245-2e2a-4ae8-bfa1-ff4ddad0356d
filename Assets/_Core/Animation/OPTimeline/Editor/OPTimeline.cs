using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using UnityEngine.UIElements;

namespace OnePuz.OPTimeline.Editor
{
    /// <summary>
    /// The Actual Timeline of the OPAnimator
    /// </summary>
    public class OPTimeline : VisualElement,IOPUpdatable
    {
        public const float TimelineClipHeight = 30;
        public const float TimelineClipSpace = 5;

        public OPAnimator Animator { get; }
        public OPAnimatorEditor AnimatorEditor { get; }

        public OPTimelineHeaderSection Header {get; private set; }
        public OPTimelineTimeSection TimeSection {get; private set; }
        public OPTimelineContentSection ContentSection {get; private set; }
        public OPTimelineFooterSection Footer {get; private set; }
        public VisualElement TopContainer {get; private set; }
        public OPAnimatorSearchWindow SearchWindow { get; }

        private readonly List<IOPUpdatable> _updatables = new List<IOPUpdatable>();
        
        public OPTimeline(OPAnimator animator, OPAnimatorEditor animatorEditor)
        {
            Animator = animator;
            AnimatorEditor = animatorEditor;
            styleSheets.Add(Resources.Load<StyleSheet>("OPTimeline"));
            AddToClassList("timeline");
            
            this.AddManipulator(new OPTimelineManipulator(this));
            
            var keys = new Dictionary<KeyCode, Action>
            {
                { KeyCode.Space, () =>
                    {
                        var guiToScreenPoint = GUIUtility.GUIToScreenPoint(Event.current.mousePosition);
                        UnityEditor.Experimental.GraphView.SearchWindow.Open(new SearchWindowContext(guiToScreenPoint), SearchWindow);
                    } 
                },
                { KeyCode.Delete, () =>
                    {
                        var clip = AnimatorEditor.GetSelectedClip();
                        if(clip == null) return;
                        ContentSection.DeleteClip(clip);
                    } 
                },
                {KeyCode.C, () =>
                    {
                        var clip = AnimatorEditor.GetSelectedClip();
                        if(clip == null) return;
                        AnimatorEditor.CopyClip(clip);
                    }
                },
                {KeyCode.D, () =>
                    {
                        var clip = AnimatorEditor.GetSelectedClip();
                        if(clip == null) return;
                        ContentSection.DuplicateClip(clip);
                    }
                },
                {KeyCode.V, () =>
                {
                    ContentSection.TryPasteCopiedClip();
                }},
            };
            this.AddManipulator(new TimelineShortcutsManipulator(keys));
            
            focusable = true;

            
            SearchWindow = ScriptableObject.CreateInstance<OPAnimatorSearchWindow>();
            SearchWindow.Initialize(this);
            
            DrawFull();
        }
        
        private void DrawFull()
        {
            DrawTopContainer();
            DrawHeader();
            DrawTimeSection();
            DrawContentSection();
            DrawFooter();
            TopContainer.BringToFront();
        }

        private void DrawHeader()
        {
            Header = new OPTimelineHeaderSection(this);
            _updatables.Add(Header);
            Add(Header);

        }

        private void DrawTimeSection()
        {
            TimeSection = new OPTimelineTimeSection(this);
            _updatables.Add(TimeSection);
            Add(TimeSection);
        }

        private void DrawContentSection()
        {
            ContentSection = new OPTimelineContentSection(this);
            _updatables.Add(ContentSection);
            Add(ContentSection);
            ContentSection.DestroyAndInstantiateClips();
        }

        private void DrawFooter()
        {
            Footer = new OPTimelineFooterSection(this);
            _updatables.Add(Footer);
            Add(Footer);
        }

        private void DrawTopContainer()
        {
            TopContainer = new VisualElement();
            TopContainer.pickingMode = PickingMode.Ignore;
            TopContainer.AddToClassList("top-container");
            Add(TopContainer);
            
            
        }

        public void Update()
        {
            foreach (var updateable in _updatables)
            {
                updateable.Update();
            }
        }
        
        public float GetClipYPosition(int index)
        {
            return index * (OPTimeline.TimelineClipHeight + OPTimeline.TimelineClipSpace) + OPTimeline.TimelineClipSpace * .5f;
        }
        
        public int GetClipIndex(float yPosition)
        {
            return Mathf.FloorToInt((yPosition + OPTimeline.TimelineClipHeight * .5f) / (OPTimeline.TimelineClipHeight + OPTimeline.TimelineClipSpace)) ;
        }
        
        public float GetTimelineWidth()
        {
            return ContentSection.layout.width;
        }
        
        public float GetWidthPerSecond()
        {
            return GetTimelineWidth() / Animator.FullDuration;
        }
        
        public float GetWidthPerSecond(float width)
        {
            return width / Animator.FullDuration;
        }
        
        public float GetSecondsPerWidth()
        {
            return Animator.FullDuration / GetTimelineWidth();
        }
        
        public float GetSecondsPerWidth(float width)
        {
            return Animator.FullDuration / width;
        }

        public void CreateNewClip(Type clipType, string clipName)
        {
            var clipInstance = (OPClip)Activator.CreateInstance(clipType);
            clipInstance.Name = clipName;
            clipInstance.StartAt = 0f;
            Undo.RecordObject(Animator,"Add Clip");
            Animator.AddClip(clipInstance);
            var color = OPSettings.GetOrCreateSettings().GetRandomColor();
            clipInstance.Color = color;
            ContentSection.AddTimelineClip(clipInstance);
            AnimatorEditor.SetSelectedClip(clipInstance);
        }
        
        public void AddClip(OPClip clip)
        {
            Undo.RecordObject(Animator,"Add Clip");
            Animator.AddClip(clip);
            ContentSection.AddTimelineClip(clip);
        }
        
        

    }
}