// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

namespace OnePuz.InputHandler
{
	public enum KeyboardKey
	{
		None = 0,
		Backspace = 8,
		Tab = 9,
		//Clear = 12,
		Return = 13,
		Pause = 19,
		Escape = 27,
		Space = 32,
		/*Exclaim = 33,     // !
		DoubleQuote = 34,   // "
		Hash = 35,          // #
		Dollar = 36,        // $
		Ampersand = 38,*/   // &
		Quote = 39,         // ' (Apostrophe)
		/*LeftParen = 40,   // (
		RightParen = 41,    // )
		Asterisk = 42,      // *
		Plus = 43,*/        // +
		Comma = 44,         // ,
		Minus = 45,         // -
		Period = 46,        // .
		Slash = 47,         // /
		/*Colon = 58,*/     // :
		Semicolon = 59,     // ;
		/*Less = 60,*/      // <
		Equals = 61,        // =
		/*Greater = 62,     // >
		Question = 63,      // ?
		At = 64,*/          // @
		LeftBracket = 91,   // [
		Backslash = 92,     // \
		RightBracket = 93,  // ]
		/*Caret = 94,       // ^
		Underscore = 95,*/  // _
		BackQuote = 96,     // ` (Grave accent)
		Num0 = 48,
		Num1 = 49,
		Num2 = 50,
		Num3 = 51,
		Num4 = 52,
		Num5 = 53,
		Num6 = 54,
		Num7 = 55,
		Num8 = 56,
		Num9 = 57,
		A = 97,
		B = 98,
		C = 99,
		D = 100,
		E = 101,
		F = 102,
		G = 103,
		H = 104,
		I = 105,
		J = 106,
		K = 107,
		L = 108,
		M = 109,
		N = 110,
		O = 111,
		P = 112,
		Q = 113,
		R = 114,
		S = 115,
		T = 116,
		U = 117,
		V = 118,
		W = 119,
		X = 120,
		Y = 121,
		Z = 122,
		Keypad0 = 256,
		Keypad1 = 257,
		Keypad2 = 258,
		Keypad3 = 259,
		Keypad4 = 260,
		Keypad5 = 261,
		Keypad6 = 262,
		Keypad7 = 263,
		Keypad8 = 264,
		Keypad9 = 265,
		KeypadPeriod = 266,     // .
		KeypadDivide = 267,     // /
		KeypadMultiply = 268,   // *
		KeypadMinus = 269,      // -
		KeypadPlus = 270,       // +
		KeypadEquals = 272,     // =
		KeypadEnter = 271,
		UpArrow = 273,
		DownArrow = 274,
		RightArrow = 275,
		LeftArrow = 276,
		Delete = 127,
		Insert = 277,
		Home = 278,
		End = 279,
		PageUp = 280,
		PageDown = 281,
		F1 = 282,
		F2 = 283,
		F3 = 284,
		F4 = 285,
		F5 = 286,
		F6 = 287,
		F7 = 288,
		F8 = 289,
		F9 = 290,
		F10 = 291,
		F11 = 292,
		F12 = 293,
		//F13 = 294,
		//F14 = 295,
		//F15 = 296,
		Numlock = 300,
		CapsLock = 301,
		ScrollLock = 302,
		RightShift = 303,
		LeftShift = 304,
		RightControl = 305,
		LeftControl = 306,
		RightAlt = 307,
		LeftAlt = 308,
		RightCommand = 309,
		LeftCommand = 310,
		LeftWindows = 311,
		RightWindows = 312,
		AltGr = 313,
		//Help = 315,
		Print = 316,
		//SysReq = 317,
		//Break = 318,
		Menu = 319,
		OEM1 = 401,
		OEM2 = 402,
		OEM3 = 403,
		OEM4 = 404,
		OEM5 = 405
	}
}
