// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

namespace OnePuz.InputHandler
{
	public enum GamepadButtonRaw
	{
		None = 0,
		Button_1 = 1,
		But<PERSON>_2 = 2,
		But<PERSON>_3 = 3,
		But<PERSON>_4 = 4,
		<PERSON><PERSON>_5 = 5,
		<PERSON><PERSON>_6 = 6,
		<PERSON><PERSON>_7 = 7,
		<PERSON><PERSON>_8 = 8,
		<PERSON><PERSON>_9 = 9,
		<PERSON><PERSON>_10 = 10,
		<PERSON><PERSON>_11 = 11,
		But<PERSON>_12 = 12,
		But<PERSON>_13 = 13,
		But<PERSON>_14 = 14,
		But<PERSON>_15 = 15,
		But<PERSON>_16 = 16,
		But<PERSON>_17 = 17,
		But<PERSON>_18 = 18,
		But<PERSON>_19 = 19,
		But<PERSON>_20 = 20
	}
}
