using System.Collections.Generic;
using Apps<PERSON>lyerSDK;
using Cysharp.Threading.Tasks;
using Firebase.Analytics;
using Firebase.Extensions;
using UnityEngine;

namespace OnePuz.Services
{
    public class NativePluginService : IServiceLoad
    {
        public bool IsFirebaseReadyToUse { get; private set; } = false;
        
        public void Load()
        {
            AppsFlyer.setHost("", "appsflyersdk.com");
            
            var isPlayServiceAvailable = Application.isEditor || IsPlayServicesAvailable(out var checkPlayServiceCode);
            if (isPlayServiceAvailable)
                InitFirebase();
            
            WaitForLogQueuedEventsAsync().Forget();
            WaitFirebaseForFetchingRemoteAsync().Forget();
        }
        
        private async UniTask WaitForLogQueuedEventsAsync()
        {
            await UniTask.WaitUntil(() => IsFirebaseReadyToUse || Time.realtimeSinceStartup >= 10f);

            Core.Analytic.FirebaseLogQueuedEvents();
        }

        private async UniTask WaitFirebaseForFetchingRemoteAsync()
        {
            OLogger.Log("Start waiting Firebase for fetching remote!");
            await UniTask.WaitUntil(() => IsFirebaseReadyToUse);

            OLogger.Log("Firebase ready to use");
            
            FirebaseAnalytics.SetConsent(new Dictionary<ConsentType, ConsentStatus>()
            {
                { ConsentType.AnalyticsStorage, ConsentStatus.Granted },
                { ConsentType.AdStorage, ConsentStatus.Granted },
                { ConsentType.AdUserData, ConsentStatus.Granted },
                { ConsentType.AdPersonalization, ConsentStatus.Granted }
            });

            Core.Get<RemoteService>().FetchData();
        }

        private void InitFirebase()
        {
            if (Application.isEditor)
            {
                IsFirebaseReadyToUse = true;
                Core.Get<AnalyticService>().ConfirmFirebaseReadyToUse();
            }
            else
            {
                Firebase.FirebaseApp.CheckAndFixDependenciesAsync().ContinueWithOnMainThread(task =>
                {
                    var dependencyStatus = task.Result;
                    if (dependencyStatus == Firebase.DependencyStatus.Available)
                    {
                        // Set a flag here indicating that Firebase is ready to use by your application.
                        IsFirebaseReadyToUse = true;
                        Core.Get<AnalyticService>().ConfirmFirebaseReadyToUse();

                        var app = Firebase.FirebaseApp.DefaultInstance;
                    }
                    else
                    {
                        OLogger.LogError($"Could not resolve all Firebase dependencies: {dependencyStatus}");
                        // Firebase Unity SDK is not safe to use here.
                    }
                });
            }
        }

        private bool IsPlayServicesAvailable(out int resultCode)
        {
            resultCode = 0;
            try
            {
                const string GoogleApiAvailability_Classname =
                    "com.google.android.gms.common.GoogleApiAvailability";
                var clazz =
                    new AndroidJavaClass(GoogleApiAvailability_Classname);
                var obj =
                    clazz.CallStatic<AndroidJavaObject>("getInstance");

                var androidJC = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                var activity = androidJC.GetStatic<AndroidJavaObject>("currentActivity");

                resultCode = obj.Call<int>("isGooglePlayServicesAvailable", activity);
                OLogger.Log($"Is Play Service Available: {resultCode}");

                // result codes from https://developers.google.com/android/reference/com/google/android/gms/common/ConnectionResult
                // 0 == success
                // 1 == service_missing
                // 2 == update service required
                // 3 == service disabled
                // 18 == service updating
                // 9 == service invalid
                return resultCode == 0;
            }
            catch (System.Exception exc)
            {
                OLogger.Log($"IsPlayServiceAvailable Exception: {exc.Message}");
                return false;
            }
        }
    }
}