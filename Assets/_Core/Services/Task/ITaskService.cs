// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;
using OnePuz.Handlers;
using OnePuz.Promises;
using OnePuz.TimeHandler;

namespace OnePuz.Services
{
	public interface ITaskService
	{
		void RunOnMainThread(Action action, float delay = 0f);

		IPromise WaitUntil(ILinkHandler handle, Func<bool> condition);
		IPromise <PERSON>hile(ILinkHandler handle, Func<bool> condition);
		IPromise WaitForSeconds<T>(ILinkHandler handle, float seconds) where T : ITimeDeltaEvent;
		IPromise WaitForTicks<T>(ILinkHandler handle, int ticks) where T : ITimeEvent;
	}
}
