// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;
using OnePuz.Handlers;
using ChannelId = System.Byte;

namespace OnePuz.Services
{
    public interface IEventBusService
    {
        void Subscribe<T>(ChannelId channel, ILinkHandler handle, Action<T> action, int priority = 0);
        void Unsubscribe<T>(ChannelId channel, Action<T> action);

        void Fire<T>(ChannelId channel, T data);
        void FireAsync<T>(ChannelId channel, T data);

        void FireSticky<T>(ChannelId channel, T data);
        void FireStickyAsync<T>(ChannelId channel, T data);

        T GetSticky<T>(ChannelId channel);
        void ClearSticky<T>(ChannelId channel);
        void ClearAllSticky();

        void StopCurrentEventPropagation();
    }
}