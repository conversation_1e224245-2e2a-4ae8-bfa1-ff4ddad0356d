using System;
using System.Linq;
using OnePuz.Extensions;
using UnityEditor;
using UnityEngine;

namespace OnePuz.Attributes.Editor
{
    [CustomPropertyDrawer(typeof(PreAssignedAttribute))]
    public class PreAssignedDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            property.serializedObject.Update();
            if (property.propertyType != SerializedPropertyType.ObjectReference)
            {
                EditorGUI.BeginDisabledGroup(true);
                EditorGUI.PropertyField(position, property, label);
                EditorGUI.EndDisabledGroup();
                EditorGUILayout.HelpBox("This attribute not support for field type.", MessageType.Error);
                property.serializedObject.ApplyModifiedProperties();
                return;
            }

            if (property.objectReferenceValue == null)
            {
                var customizedAttribute = (PreAssignedAttribute)attribute;

                if (customizedAttribute.hasDefaultReference)
                {
                    EditorGUI.PropertyField(new Rect(position) { width = position.width - 100 - 2 }, property, label);
                    GUI.color = PresetGUIColor.lightGreen;
                    if (G<PERSON>.Button(new Rect(position) { width = 100 - 2, x = position.x + position.width - 100 + 2 }, "As Default"))
                    {
                        var script = (MonoScript)Resources.FindObjectsOfTypeAll(typeof(MonoScript))
                            .FirstOrDefault(arg => string.CompareOrdinal(arg.name , property.serializedObject.targetObject.GetType().Name) == 0);
        
                        if (script != null)
                        {
                            var monoImporter = (MonoImporter)AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(script));
                            if (monoImporter != null)
                            {
                                var defaultValue = monoImporter.GetDefaultReference(fieldInfo.Name);
                                if (defaultValue != null)
                                {
                                    property.objectReferenceValue = defaultValue;
                                }
                            }
                        }
                    }
                    GUI.color = Color.white;
                    property.serializedObject.ApplyModifiedProperties();
                    return;
                }
                
                SerializedProperty rootProperty = null;
                if (!string.IsNullOrEmpty(customizedAttribute.rootName))
                    rootProperty = property.serializedObject.FindProperty(customizedAttribute.rootName);
                
                EditorGUI.PropertyField(new Rect(position) { width = position.width - 100 - 2 }, property, label);
                
                if (!string.IsNullOrEmpty(customizedAttribute.path))
                {
                    GUI.color = PresetGUIColor.lightBlue;
                    if (GUI.Button(new Rect(position) { width = 100 - 2, x = position.x + position.width - 100 + 2 }, "Find"))
                    {
                        if (rootProperty == null)
                        {
                            var targetObject = ((Component)property.serializedObject.targetObject).transform;
                            property.objectReferenceValue = targetObject.transform.FindByPath(customizedAttribute.path, fieldInfo.FieldType);
                        }
                        else
                        {
                            if (rootProperty.objectReferenceValue != null)
                            {
                                property.objectReferenceValue = (rootProperty.objectReferenceValue switch
                                {
                                    GameObject arg => arg.transform,
                                    Transform arg => arg,
                                    Component arg => arg.transform,
                                    _ => throw new ArgumentOutOfRangeException()
                                }).FindByPath(customizedAttribute.path, fieldInfo.FieldType);
                            }
                        }
                    }

                    GUI.color = Color.white;

                    if (rootProperty != null)
                    {
                        if (rootProperty.objectReferenceValue == null)
                        {
                            GUILayout.Space(EditorGUIUtility.singleLineHeight);
                            EditorGUI.HelpBox(new Rect(position)
                                {
                                    width = position.width - EditorGUIUtility.labelWidth - 2, x = position.x + EditorGUIUtility.labelWidth + 2,
                                    y = position.y + EditorGUIUtility.singleLineHeight + 2
                                }
                                , $"\'{customizedAttribute.rootName}\' is null", MessageType.Error);
                            
                            GUILayout.Space(EditorGUIUtility.singleLineHeight);
                            EditorGUI.LabelField(new Rect(position) { y = position.y + 2 * EditorGUIUtility.singleLineHeight + 2 },
                                " ",
                                $"{rootProperty.name}/{customizedAttribute.path}", PresetGUIStyle.subtitle);
                        }
                        else
                        {
                            GUILayout.Space(EditorGUIUtility.singleLineHeight);
                            EditorGUI.LabelField(new Rect(position) { y = position.y + EditorGUIUtility.singleLineHeight + 2},
                                " ",
                                $"{rootProperty.name}/{customizedAttribute.path}", PresetGUIStyle.subtitle);
                        }
                        
                        
                    }
                    else
                    {
                        GUILayout.Space(EditorGUIUtility.singleLineHeight);
                        EditorGUI.LabelField(new Rect(position) { y = position.y + EditorGUIUtility.singleLineHeight + 2 }, " ",
                            customizedAttribute.path, PresetGUIStyle.subtitle);
                    }
                }
                else
                {
                    GUI.color = PresetGUIColor.lightOrange;
                    if (GUI.Button(new Rect(position) { width = 100 - 2, x = position.x + position.width - 100 + 2 }, "Get"))
                    {
                        if (rootProperty == null)
                        {
                            var targetObject = (Component)property.serializedObject.targetObject;
                            property.objectReferenceValue = targetObject.GetObject(fieldInfo.FieldType);
                        }
                        else
                        {
                            property.objectReferenceValue = (rootProperty.objectReferenceValue switch
                            {
                                GameObject arg => arg.transform,
                                Component arg => arg,
                                _ => throw new ArgumentOutOfRangeException()
                            }).GetObject(fieldInfo.FieldType);
                        }
                    }
                    GUI.color = Color.white;
                }
            }
            else
            {
                EditorGUI.BeginDisabledGroup(true);
                EditorGUI.PropertyField(new Rect(position) { width = position.width - 100 - 2 }, property, label);
                EditorGUI.EndDisabledGroup();
                GUI.color = PresetGUIColor.darkWhite;
                if (GUI.Button(new Rect(position) { width = 100 - 2, x = position.x + position.width - 100 + 2 }, "Fallback"))
                    property.objectReferenceValue = null;
                GUI.color = Color.white;
            }

            property.serializedObject.ApplyModifiedProperties();
        }
    }
}