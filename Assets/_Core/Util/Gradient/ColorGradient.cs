// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;

namespace OnePuz.Gradient
{
	using OnePuz.Util;

	[Serializable]
	public class ColorGradient : Gradient<Color>
	{
		public ColorGradient()
		{
			SetLerp(LerpClamped);
		}

		private static Color LerpClamped(Color a, Color b, float alpha)
		{
			alpha = Clamp01(alpha);

			return a * (1f - alpha) + b * alpha;
		}

		private static float Clamp01(float value)
		{
			return Math.Min(1f, Math.Max(0f, value));
		}
	}
}
