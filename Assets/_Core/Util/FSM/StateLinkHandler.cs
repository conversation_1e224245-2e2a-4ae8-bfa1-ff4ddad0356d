// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;
using OnePuz.Handlers;

namespace OnePuz.FSM
{
	public struct StateLinkHandler : ILinkHandler
	{
		private readonly WeakReference stateRef;

		public StateLinkHandler(State state)
		{
			this.stateRef = new WeakReference(state);
		}

		public bool IsActive => !IsDestroyed && ((State)stateRef.Target).IsCurrent();
		public bool IsDestroyed => !stateRef.IsAlive;
	}
}
