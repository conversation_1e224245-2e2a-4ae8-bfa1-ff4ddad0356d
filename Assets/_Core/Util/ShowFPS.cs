using UnityEngine;

namespace OnePuz.Utilities
{
    public class ShowFPS : MonoBehaviour
    {
        private float _deltaTime;
        private GUIStyle style;
        private Rect rect;
        //private Rect rect1;

        float msec;
        float fps;
        string text;

        //System.Text.StringBuilder builder;

        //private static ShowFPS instance;
        //private void Awake()
        //{
        //    if (instance == null)
        //    {
        //        instance = this;
        //        DontDestroyOnLoad(gameObject);
        //    }
        //    else
        //    {
        //        Destroy(gameObject);
        //    }
        //}
        public static ShowFPS Instance { get; private set; }
        void Awake()
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            gameObject.SetActive(false);
        }

        void Start()
        {
            int w = Screen.width, h = Screen.height;
            style = new GUIStyle();
            rect = new Rect(0, 0, w, h * 2 / 100);
            //rect1 = new Rect(0, 40, w, h * 2 / 100);

            style.alignment = TextAnchor.UpperLeft;
            style.fontSize = h * 1 / 60;
            style.normal.textColor = new Color(0.0f, 1.0f, 0.0f, 1.0f);

            //builder = new System.Text.StringBuilder();
            //builder.AppendLine("graphicsShaderLevel:{0}");
            //builder.AppendLine("graphicsMemorySize:{1}");
            //builder.AppendLine("processorCount:{2}");
            //builder.AppendLine("processorFrequency:{3}");
            //builder.AppendLine("hdrDisplaySupportFlags:{4}");
            //builder.AppendLine("supportsComputeShaders:{5}");
            //builder.AppendLine("supportsGeometryShaders:{6}");
            //builder.AppendLine("supportsGraphicsFence:{7}");
        }

        void Update()
        {
            _deltaTime += (Time.unscaledDeltaTime - _deltaTime) * 0.1f;
        }
        void OnGUI()
        {
            if (Time.frameCount % 60 == 0)
            {
                msec = _deltaTime * 1000.0f;
                fps = 1.0f / _deltaTime;
                text = string.Format("{0:0.0} ms ({1:0.} fps)", msec, fps);
            }
            GUI.Label(rect, text, style);
            //GUI.Label(rect1, string.Format(builder.ToString(),
            //                               SystemInfo.graphicsShaderLevel,
            //                               SystemInfo.graphicsMemorySize,
            //                               SystemInfo.processorCount,
            //                               SystemInfo.processorFrequency,
            //                               SystemInfo.hdrDisplaySupportFlags,
            //                               SystemInfo.supportsComputeShaders,
            //                               SystemInfo.supportsGeometryShaders,
            //                               SystemInfo.supportsGraphicsFence
            //                               ), style);
        }
    }
}