// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;
using OnePuz.FSM;
using OnePuz.Handlers;
using OnePuz.Observables;
using OnePuz.Services;

namespace OnePuz.Observables
{
	public static class ObservableExtensions
	{
		public static void Subscribe<T>(this Observable<T> observable, IService service, Action<T> action)
		{
			observable.Subscribe(new ObjectLinkHandler<IService>(service), action);
		}

		public static void Subscribe<T>(this Observable<T> observable, State state, Action<T> action)
		{
			observable.Subscribe(new StateLinkHandler(state), action);
		}
	}
}
