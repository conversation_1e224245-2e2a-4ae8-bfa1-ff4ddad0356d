using System;
using System.Collections.Generic;
using UnityEngine;

namespace OnePuz.Extensions
{
    public static class IEnumerableExtension
    {
        public static void Shuffle<T>(this IList<T> list)
        {
            var count = list.Count;
            for (var i = count - 1; i > 0; --i)
            {
                var randomIndex = (0, i + 1).Random();
                (list[i], list[randomIndex]) = (list[randomIndex], list[i]);
            }
        }

        public static int GetMaxValue(this IList<int> list)
        {
            var result = 0;
            foreach (var entry in list)
            {
                if (result > entry)
                    result = entry;
            }

            return result;
        }

        public static IList<T> Clone<T>(this IList<T> list)
        {
            var result = new List<T>();
            foreach (var entry in list)
                result.Add(entry);
            return result;
        }

        public static void Clear<T>(this T[] array) where T : struct
        {
            for (var index = 0; index < array.Length; index++)
                array[index] = default;
        }

        public static void Clear(this Component[] array)
        {
            for (var index = 0; index < array.Length; index++)
                array[index] = null;
        }

        public static T[] Remove<T>(this T[] source, T item) where T : class
        {
            int index = Array.FindIndex(source, obj => obj == item);
            if (index < 0)
                throw new Exception("Not found \'item\'.");

            return source.RemoveAt(index);
        }

        public static T[] RemoveAt<T>(this T[] source, int index)
        {
            if (index < 0 || index >= source.Length)
            {
                throw new ArgumentOutOfRangeException(nameof(index), "\'index\' is out of range.");
            }

            T[] result = new T[source.Length - 1];
            if (index > 0)
            {
                Array.Copy(source, 0, result, 0, index);
            }

            if (index < source.Length - 1)
            {
                Array.Copy(source, index + 1, result, index, source.Length - index - 1);
            }

            return result;
        }

        public static T[] RemoveRange<T>(this T[] source, int index, int range)
        {
            if (index < 0 || index >= source.Length)
            {
                throw new ArgumentOutOfRangeException(nameof(index), "\'index\' is out of range.");
            }

            T[] result = new T[source.Length - range];
            if (index > 0)
            {
                Array.Copy(source, 0, result, 0, index);
            }

            if (index < source.Length - 1)
            {
                Array.Copy(source, index + range, result, index, source.Length - range - index);
            }

            return result;
        }

        public static List<int> ToList(this (int, int) range)
        {
            var result = new List<int>(range.Item2 - range.Item1);
            for (var i = range.Item1; i < range.Item2; i++)
                result.Add(i);
            return result;
        }
        
        public static bool TryAdd<T>(this List<T> list, T value)
        {
            if (list.Contains(value))
                return false;
            list.Add(value);
            return true;
        }
        
        public static bool Contain<T>(this T[] array, T arg) => Array.IndexOf(array, arg) >= 0;

        public static T Random<T>(this IList<T> args) => args[(0, args.Count).Random(true)];

        public static T GetBottomMostElement<T>(this IList<T> list) where T : Component
            => list.GetOutermostElement(entry => entry.position.y, (arg1, arg2) => arg1 < arg2);

        public static T GetTopMostElement<T>(this IList<T> list) where T : Component
            => list.GetOutermostElement(entry => entry.position.y, (arg1, arg2) => arg1 > arg2);

        public static T GetLeftMostElement<T>(this IList<T> list) where T : Component
            => list.GetOutermostElement(entry => entry.position.x, (arg1, arg2) => arg1 < arg2);

        public static T GetRightMostElement<T>(this IList<T> list) where T : Component
            => list.GetOutermostElement(entry => entry.position.x, (arg1, arg2) => arg1 > arg2);

        private static T GetOutermostElement<T>(this IList<T> list, Func<Transform, float> comparisonFactor, Func<float, float, bool> comparison)
            where T : Component
        {
            T result = null;
            float? t = null;
            foreach (var item in list)
            {
                var entry = item as Transform ?? item.transform;
                if (!t.HasValue)
                {
                    t = comparisonFactor(entry);
                    result = item;
                    continue;
                }

                if (comparison(t.Value, comparisonFactor(entry)))
                    continue;

                t = comparisonFactor(entry);
                result = item;
            }

            return result;
        }

        public static T GetItemNearest<T>(this IReadOnlyList<T> list, T compareObject) where T : Component
        {
            var result = list[0];
            var resultTransform = result as Transform ?? result.transform;
            var compareTransform = compareObject as Transform ?? compareObject.transform;
            for (var i = 1; i < list.Count; i++)
            {
                var entryTransform = list[i] as Transform ?? list[i].transform;
                if (Vector3.Distance(entryTransform.position, compareTransform.position) >
                    Vector3.Distance(resultTransform.position, compareTransform.position))
                    continue;

                result = list[i];
                resultTransform = result as Transform ?? result.transform;

            }

            return result;
        }

        public static Vector3 GetFarthestElement(this IList<Vector3> list, Vector3 target)
            => list.GeElement((arg1, arg2) => Vector3.Distance(arg1, target) > Vector3.Distance(arg2, target));

        public static Vector3 GetNearestElement(this IList<Vector3> list, Vector3 target)
            => list.GeElement((arg1, arg2) => Vector3.Distance(arg1, target) < Vector3.Distance(arg2, target));

        public static Vector2 GetFarthestElement(this IList<Vector2> list, Vector2 target)
            => list.GeElement((arg1, arg2) => Vector2.Distance(arg1, target) > Vector2.Distance(arg2, target));

        public static Vector2 GetNearestElement(this IList<Vector2> list, Vector2 target)
            => list.GeElement((arg1, arg2) => Vector2.Distance(arg1, target) < Vector2.Distance(arg2, target));

        private static T GeElement<T>(this IList<T> list, Func<T, T, bool> comparison) where T : struct
        {
            var result = list[0];
            for (var i = 1; i < list.Count; i++)
            {
                if (!comparison(list[i], result))
                    continue;

                result = list[i];
            }

            return result;
        }
    }
}