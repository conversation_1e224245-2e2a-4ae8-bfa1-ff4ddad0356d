using System;
using System.Collections.Generic;
using UnityEngine;

namespace _FeatureHub.Utilities
{
    public class UndoService : IDisposable
    {
        public delegate void UndoAction<in TTarget, in TTrack>(TTarget target, TTrack tracked);
        public delegate void UndoAction<in TTarget>(TTarget target);

        private readonly LinkedList<(int frameCount, object target, object tracked, UndoAction<object, object> executor)> undoLinked = new();

        public void Record<TTarget, TTrack>(TTarget target, TTrack tracking, UndoAction<TTarget, TTrack> onRewound) where TTarget : class
        {
            undoLinked.AddFirst((Time.frameCount, target, tracking, (t, v) => onRewound(t as TTarget, (TTrack)v)));
        }
        
        public void Record<TTarget>(TTarget target, UndoAction<TTarget> onRewound) where TTarget : class
        {
            undoLinked.AddFirst((Time.frameCount, target, null, (t, _) => onRewound(t as TTarget)));
        }

        public bool Invoke()
        {
            if (undoLinked.Count == 0)
                return false;
            var node = undoLinked.First;
            var lastFrame = node.Value.frameCount;
            while (node != null)
            {
                if(node.Value.frameCount != lastFrame)
                    break;
                node.Value.executor.Invoke(node.Value.target, node.Value.tracked);
                node = node.Next;
                undoLinked.RemoveFirst();
            }

            return true;
        }

        public void Extrude<TTarget, TTracked>(Func<TTarget, TTracked, bool> comparison)
        {
            var stepsToExtrude = new List<int>();
            foreach (var entry in undoLinked)
            {
                if (entry is not { target: TTarget target, tracked: TTracked tracked })
                    continue;
                if (comparison(target, tracked))
                    stepsToExtrude.Add(entry.frameCount);
            }

            var node = undoLinked.First;
            if(node is null)
                return;
            
            while (node != null)
            {
                if (stepsToExtrude.Contains(node.Value.frameCount))
                {
                    var extruded = node;
                    node = node.Next;
                    undoLinked.Remove(extruded);
                    continue;
                }

                node = node.Next;
            }
        }

        public void Extrude(int steps)
        {
            var node = undoLinked.First;
            if(node is null)
                return;
            var lastFrame = node.Value.frameCount;
            while (node != null && steps > 0)
            {
                if (node.Value.frameCount != lastFrame)
                {
                    lastFrame = node.Value.frameCount;
                    steps--;
                    continue;
                }

                node = node.Next;
                undoLinked.RemoveFirst();
            }
        }

        public void Dispose()
        {
            undoLinked.Clear();
        }
    }
}