using System;
using System.Globalization;
using Newtonsoft.Json;
using UnityEngine;

// ReSharper disable MemberCanBePrivate.Global

namespace _FeatureHub.Utilities
{
    public abstract class BasePrefs : PlayerPrefs
    {
        public new static int GetInt(string key, int defaultValue = 0)
        {
            if(<PERSON><PERSON><PERSON>(key))
                return PlayerPrefs.GetInt(key, defaultValue);
            SetInt(key, defaultValue);
            return defaultValue;
        }
        
        public static bool GetBool(string key, bool defaultValue = false)
        {
            if(<PERSON><PERSON><PERSON>(key))
                return PlayerPrefs.GetInt(key, defaultValue ? 1 : 0) == 1;
            
            SetBool(key, defaultValue);
            return defaultValue;
        }
        public static void SetBool(string key, bool value)
        {
            SetInt(key, value ? 1 : 0);
        }

        public static T GetEnum<T>(string key, T defaultValue) where T : Enum
        {
            if (<PERSON><PERSON><PERSON>(key))
                return Enum.TryParse(typeof(T), GetString(key, defaultValue.ToString()), out var result) ? (T)result : defaultValue;
            SetEnum(key, defaultValue);
            return defaultValue;
        }

        public static void SetEnum<T>(string key, T value) where T : Enum
        {
            SetString(key, value.ToString());
        }

        public static T GetJson<T>(string key, T defaultValue)
        {
            if(HasKey(key))
               return JsonConvert.DeserializeObject<T>(GetString(key));
            
            SetJson(key, defaultValue);
            return defaultValue;
        }
        
        public static object GetJson(string key, Type type)
        {
            if(HasKey(key))
                return JsonConvert.DeserializeObject(GetString(key), type);
            return null;
        }

        public static void SetJson<T>(string key, T value)
        {
            SetString(key, JsonConvert.SerializeObject(value));
            Save();
        }

        public static double GetDouble(string key, double defaultValue = 0.0)
        {
            if(HasKey(key))
                return double.TryParse(GetString(key, defaultValue.ToString(CultureInfo.CurrentCulture)), out var result) ? (double)result : defaultValue;
            
            SetDouble(key, defaultValue);
            return defaultValue;
        }
        
        public static void SetDouble(string key, double value)
        {
            SetString(key, value.ToString(CultureInfo.CurrentCulture));
        }

        public static long GetLong(string key, long defaultValue)
        {
            SplitLong(defaultValue, out var lowBits, out var highBits);
            // lowBits = GetInt(key + "_lowBits", lowBits);
            // highBits = GetInt(key + "_highBits", highBits);

            // unsigned, to prevent loss of sign bit.
            ulong ret = (uint)highBits;
            ret <<= 32;
            return (long)(ret | (uint)lowBits);
        }

        public static long GetLong(string key)
        {
            var lowBits = GetInt($"{key}_lowBits");
            var highBits = GetInt($"{key}_highBits");

            // unsigned, to prevent loss of sign bit.
            ulong ret = (uint)highBits;
            ret <<= 32;
            return (long)(ret | (uint)lowBits);
        }

        public static void SetLong(string key, long value)
        {
            SplitLong(value, out var lowBits, out var highBits);
            SetInt($"{key}_lowBits", lowBits);
            SetInt($"{key}_highBits", highBits);
        }

        private static void SplitLong(long input, out int lowBits, out int highBits)
        {
            // unsigned everything, to prevent loss of sign bit.
            lowBits = (int)(uint)(ulong)input;
            highBits = (int)(uint)(input >> 32);
        }
    }
}
