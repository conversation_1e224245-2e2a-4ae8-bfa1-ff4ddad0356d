using System.Collections;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace _FeatureHub.Utilities
{
    public abstract class SceneOperator : MonoBehaviour
    {
        public static class Delegate
        {
            public delegate void SceneEvent(Scene scene);
        }
        
        public static class Event
        {
            public static event Delegate.SceneEvent onSceneUnload
            {
                add => m_Instance.m_OnSceneUnload += value;
                remove => m_Instance.m_OnSceneUnload -= value;
            }
            
            public static event Delegate.SceneEvent onSceneLoad
            {
                add => m_Instance.m_OnSceneLoad += value;
                remove => m_Instance.m_OnSceneLoad -= value;
            }
        }

        private static SceneOperator m_Instance;
        private event Delegate.SceneEvent m_OnSceneUnload;
        private event Delegate.SceneEvent m_OnSceneLoad;

        public static void Awakening()
        {
            if (m_Instance != null)
                return;
            m_Instance = Instantiate(Resources.Load<SceneOperator>("SceneOperator"));
        }

        protected virtual void Awake()
        {
            if (m_Instance == null)
            {
                m_Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }


        public static void Load(string sceneName, float delayFadeOut = 0.5f)
        {
            m_Instance.StartCoroutine(LoadRoutine(sceneName, delayFadeOut));
        }

        private static IEnumerator LoadRoutine(string sceneName, float delayFadeOut)
        {
            yield return m_Instance.FadeIn();
            m_Instance.m_OnSceneUnload?.Invoke(SceneManager.GetActiveScene());
            m_Instance.m_OnSceneUnload = null;
            yield return null;
            var operation =  SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Single);
            Debug.Assert(operation != null, $"{nameof(operation)} != null");
            operation.allowSceneActivation = false;
            while (!operation.isDone)
            {
                if (operation.progress >= 0.9f)
                    operation.allowSceneActivation = true;
                yield return null;
            }
            if (delayFadeOut > 0)
                yield return new WaitForSeconds(delayFadeOut);
            yield return m_Instance.FadeOut();
            m_Instance.m_OnSceneLoad?.Invoke(SceneManager.GetActiveScene());
            m_Instance.m_OnSceneLoad = null;
        }

        protected abstract IEnumerator FadeIn();
        protected abstract IEnumerator FadeOut();
        
    }
}