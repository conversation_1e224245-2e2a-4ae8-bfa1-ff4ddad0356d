using Sirenix.OdinInspector;
using UnityEngine;

namespace OnePuz.Definition
{
    public enum BoosterType 
    {
        UNDO,
        MORE_SLOTS,
        SHUFFLE,
        MAGNET
    }

    [System.Serializable]
    public struct Price
    {
        public int value;
        public CurrencyType type;
    }
    
    [CreateAssetMenu(menuName = "OnePuz/Definitions/BoosterDefinition")]
    public class BoosterDefinition : BaseDefinition<BoosterType, BoosterDefinitionData> { }

    [System.Serializable]
    public class BoosterDefinitionData : BaseDefinitionData<BoosterType>
    {
        public int levelUnlock;
        
        [Multiline(3)]
        public string description;
        
        [InlineProperty]
        public Price price;
        
    }
}