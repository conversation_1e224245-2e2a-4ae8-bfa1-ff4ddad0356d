using Newtonsoft.Json;

namespace OnePuz.Data
{
    [System.Serializable]
    public class LevelData : ISaveData, IVersionedData
    {
        public const int TOTAL_LEVEL = 385;
        
        [JsonProperty]
        public int Current { get; private set; }

        public int ClampCurrent => Current + 1 > TOTAL_LEVEL ? 100 + ((Current + 1 - TOTAL_LEVEL - 1) % (TOTAL_LEVEL - 100 + 1)) : Current + 1;

        public override void SetupDefaultValues()
        {
            Current = 0;
        }
        
        public int CompleteLevel()
        {
            Current++;
            return Current;
        }
        
        public int SetLevel(int level)
        {
            Current = level;
            return Current;
        }

        public int Version { get; set; } = 0;
        
        public void Migrate()
        {
            if (Version < 1)
            {
                
            }
        }
    }
}