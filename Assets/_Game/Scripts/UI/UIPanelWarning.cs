using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Audio;
using OnePuz.Extensions;
using PrimeTween;
using Spine.Unity;
using UnityEngine;

namespace OnePuz.UI
{
    public class UIPanelWarning : UIBasePanel
    {
        [SerializeField]
        private SkeletonGraphic _hardSkeletonAnimation;

        [SerializeField, SpineAnimation]
        private string _hardLevelHideAnimationName;

        [SerializeField, SpineAnimation]
        private string _hardLevelShowAnimationName;

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            AudioShortcut.PlayWarning();
            Tween.Alpha(pCanvasGroup, 0f, 1f, 0.25f).Forget(cancellationToken);
            _hardSkeletonAnimation.AnimationState.SetAnimation(0, _hardLevelShowAnimationName, false);
            await UniTask.Delay(TimeSpan.FromSeconds(_hardSkeletonAnimation.AnimationState.GetCurrent(0).Animation.Duration), cancellationToken: cancellationToken);
            Close();
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            _hardSkeletonAnimation.AnimationState.SetAnimation(0, _hardLevelHideAnimationName, false);
            var duration = _hardSkeletonAnimation.AnimationState.GetCurrent(0).Animation.Duration;
            await UniTask.Delay(TimeSpan.FromSeconds(duration * 0.8f), cancellationToken: cancellationToken);
            await Tween.Alpha(pCanvasGroup, 1f, 0f, 0.25f).WithCancellation(cancellationToken);
        }
    }
}