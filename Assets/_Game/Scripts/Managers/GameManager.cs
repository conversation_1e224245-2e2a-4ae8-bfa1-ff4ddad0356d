using Cysharp.Threading.Tasks;
using Kamgam.SandGame;
using OnePuz.Manager;
using UnityEngine;
using UnityEngine.EventSystems;

namespace OP.BlockSand
{
    public class GameManager : BaseManager
    {
        [SerializeField]
        private Camera _camera;

        [SerializeField]
        private SandWorld _sandWorld;

        [SerializeField]
        private PixelMaterialId _drawingMaterialId = PixelMaterialId.Sand;

        private Vector3? _lastMousePixelPos;

        public override async UniTask LoadAsync()
        {
            _sandWorld.PixelWorld.FrameRate = 60;
            await UniTask.Yield();
            var loading = true;
            _sandWorld.LoadLevel(2, _ => loading = false);
            while (loading)
                await UniTask.Yield();
        }

        public override UniTask UnloadAsync()
        {
            return UniTask.CompletedTask;
        }

        public override async UniTask ResetAsync()
        {
            await UniTask.CompletedTask;
        }

        public override void Activate()
        {
        }

        private void Update()
        {
            if (!_sandWorld.PixelWorld.LoadSucceeded)
                return;

            // Touch
            if (Input.touchCount > 0)
            {
                var touch = Input.GetTouch(0);
                var pixelPos = _sandWorld.PixelWorld.ScreenToPixelPos(touch.position);
                DrawAt(bigBrush: false, pixelPos);
            }

            // Keyboard
            else if (
                ((Input.GetMouseButton(0) && !Input.GetKey(KeyCode.LeftControl))
                 || (Input.GetMouseButtonDown(0) && Input.GetKey(KeyCode.LeftControl)))
                && !EventSystem.current.IsPointerOverGameObject()
            )
            {
                bool isShiftPressed = Input.GetKey(KeyCode.LeftShift);
                var pixelPos = _sandWorld.PixelWorld.MouseToPixelPos(Input.mousePosition);
                DrawAt(isShiftPressed, pixelPos);
            }
            else
            {
                _lastMousePixelPos = null;
            }
        }

        private void DrawAt(bool bigBrush, Vector3 pixelPos)
        {
            if (!_lastMousePixelPos.HasValue)
            {
                _sandWorld.PixelWorld.DrawBrushAt(PixelWorldBrushShape.Circle, bigBrush ? 20 : 5, pixelPos.x, pixelPos.y, _drawingMaterialId);
            }
            else
            {
                _sandWorld.PixelWorld.DrawLine(PixelWorldBrushShape.Circle, bigBrush ? 20 : 5, _lastMousePixelPos.Value.x, _lastMousePixelPos.Value.y, pixelPos.x, pixelPos.y, _drawingMaterialId);
            }

            _lastMousePixelPos = pixelPos;
        }
    }
}