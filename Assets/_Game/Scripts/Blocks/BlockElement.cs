using UnityEngine;

namespace OP.BlockSand
{
    public class BlockElement : MonoBehaviour
    {
        [SerializeField]
        private SpriteRenderer _renderer;

        [SerializeField]
        private Vector2Int _coordinate;
        
        public Vector2Int Coordinate => _coordinate;

        public void Init(Vector2Int coordinate, Sprite blockSprite)
        {
            _coordinate = coordinate;
            _renderer.sprite = blockSprite;
        }
        
        public Color[] GetPixels()
        {
            // What if spriteRenderer has color applied?
            return _renderer.sprite.texture.GetPixels();
        }
    }
}