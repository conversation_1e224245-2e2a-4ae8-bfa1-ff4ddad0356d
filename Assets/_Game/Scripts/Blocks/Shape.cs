using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

namespace OP.BlockSand
{
    public enum ColorType
    {
        Blue, Yellow, Red, Green, <PERSON>, <PERSON>, <PERSON><PERSON>
    }
    
    [CreateAssetMenu(fileName = "BlockDefinitions.asset", menuName = "BlockDefinitions")]
    public class BlockDefinitions : ScriptableObject
    {
        [Header("Settings")]
        public float blockSize;
        
        [Header("Definitions")]
        [SerializeField]
        private List<TextureDefinition> _textureDefinitions = new();

        [SerializeField]
        private List<ColorDefinition> _colorDefinitions = new();
        
        public Sprite GetRandomBlockSprite()
        {
            return _textureDefinitions[Random.Range(0, _textureDefinitions.Count)].sprite;
        }
        
        [System.Serializable]
        public class TextureDefinition
        {
            public Sprite sprite;
        }

        [System.Serializable]
        public class ColorDefinition
        {
            public ColorType type;
            public Color color;
        }
    }

    [CreateAssetMenu(fileName = "ShapeDefinitions.asset", menuName = "ShapeDefinitions")]
    public class ShapeDefinition : ScriptableObject
    {
        public List<Datum> shapes;
        
        [System.Serializable]
        public class Datum
        {
            public string id;
            public List<Vector2Int> coordinates;
        }
    }
    
    public class Shape : MonoBehaviour
    {
        [SerializeField]
        private GameObject _elementPrefab;
        
        [SerializeField]
        private BlockDefinitions _blockDefinitions;

        private List<Vector2Int> _coordinates = new ();
        private List<BlockElement> _elements = new ();

        private Rect _rect;

        public void Init(IList<Vector2Int> coordinates)
        {
            _coordinates = new List<Vector2Int>();
            _coordinates.AddRange(coordinates);
            _rect = CalculateRect();
            
            _elements.Clear();

            foreach (var coordinate in _coordinates)
            {
                var element = Core.ScenePool.Spawn(_elementPrefab, transform).GetComponent<BlockElement>();
                element.Init(coordinate, _blockDefinitions.GetRandomBlockSprite());
                element.transform.localPosition = (Vector2)coordinate * _blockDefinitions.blockSize + _rect.min;
                
                _elements.Add(element);
            }
        }

        private Color[] GetPixels()
        {
            List<Color> pixels = new ();
            
            foreach (var element in _elements)
            {
                var elementPixels = element.GetPixels();
                pixels.AddRange(elementPixels);
            }
            
            return pixels.ToArray();
        }

        private Rect CalculateRect()
        {
            var minX = float.MaxValue;
            var maxX = float.MinValue;
            var minY = float.MaxValue;
            var maxY = float.MinValue;
            
            foreach (var coordinate in _coordinates)
            {
                var position = (Vector2)coordinate * _blockDefinitions.blockSize;
                minX = Mathf.Min(minX, position.x);
                maxX = Mathf.Max(maxX, position.x);
                minY = Mathf.Min(minY, position.y);
                maxY = Mathf.Max(maxY, position.y);
            }
            
            return new Rect(minX, minY, maxX - minX, maxY - minY);
        }
    }
}