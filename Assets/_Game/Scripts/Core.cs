using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OnePuz;
using OnePuz.Audio;
using OnePuz.Manager;
using OnePuz.DailyReward.Services;
using OnePuz.Data;
using OnePuz.Data.Services;
using OnePuz.Definition;
using OnePuz.Live;
using OnePuz.Purchase;
using OnePuz.Services;
using OnePuz.UI;
using OP.BlockSand;
using UnityEngine;

public static class Core
{
    // Methods to get services from the service container

    public static T Get<T>() => ServiceContainer.Get<T>();
    public static T Get<T>(string id) => ServiceContainer.Get<T>(id);

    // Core services

    public static IEventBusService Event => ServiceContainer.Get<IEventBusService>();
    public static ITaskService Task => ServiceContainer.Get<ITaskService>();
    public static ICoroutineService Coroutine => ServiceContainer.Get<ICoroutineService>();
    public static ICacheService Cache => ServiceContainer.Get<ICacheService>();

    // Unity related services

    public static ITimeService Time => ServiceContainer.Get<ITimeService>();
    public static IUnityService Unity => ServiceContainer.Get<IUnityService>();
    public static IInputService Input => ServiceContainer.Get<IInputService>();
    public static IPrefabPoolService GlobalPool => ServiceContainer.Get<GlobalPrefabPoolService>();
    public static IPrefabPoolService ScenePool => ServiceContainer.Get<ScenePrefabPoolService>();

    // Game Core services
    
    public static PerformanceService Performance => ServiceContainer.Get<PerformanceService>();

    public static IAdService Ads => ServiceContainer.Get<IAdService>();
    
    public static NativePluginService NativePlugin => ServiceContainer.Get<NativePluginService>();
    public static AnalyticService Analytic => ServiceContainer.Get<AnalyticService>();

    public static IVibrationService Vibration => ServiceContainer.Get<IVibrationService>();
    public static void Vibrate() => Vibration.Vibrate();

    public static AudioService Audio => ServiceContainer.Get<AudioService>();

    public static IGameStateService State => ServiceContainer.Get<IGameStateService>();

    public static UIService UI => ServiceContainer.Get<UIService>();

    public static DataService Data => ServiceContainer.Get<DataService>();

    public static DefinitionService Definition => ServiceContainer.Get<DefinitionService>();

    public static PurchaseService IAP => ServiceContainer.Get<PurchaseService>();

    public static DailyRewardService DailyReward => ServiceContainer.Get<DailyRewardService>();
    
    public static EffectService Fx => ServiceContainer.Get<EffectService>();
    
    public static ILiveService Live => ServiceContainer.Get<ILiveService>();

    #region Game Shorthands

    public static void Loading() => State.ChangeState(GameState.LOADING);
    public static void Home() => State.ChangeState(GameState.HOME);
    public static void Game() => State.ChangeState(GameState.GAME);
    public static void Win() => State.ChangeState(GameState.WIN);
    public static void Lose() => State.ChangeState(GameState.LOSE);
    public static void Replay() => State.ChangeState(GameState.REPLAY);
    public static void Next() => State.ChangeState(GameState.NEXT);
    public static void Play() => State.ChangeState(GameState.PLAYING);

    public static BaseDefinitionData GetRewardDefinition(RewardData rewardData) => Definition.GetRewardDefinition(rewardData);

    public static void SetManager(BaseManager manager) => Core.Cache.Set(manager, "BaseManager", true);
    public static BaseManager Manager => Core.Cache.Get<BaseManager>("BaseManager");
    public static HomeManager HomeManager => Core.Cache.Get<BaseManager>("BaseManager") as HomeManager;
    public static GameManager GameManager => Core.Cache.Get<BaseManager>("BaseManager") as GameManager;

    #endregion
}