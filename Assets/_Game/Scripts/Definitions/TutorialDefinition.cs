using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

[CreateAssetMenu(fileName = "TutorialDefinition.asset", menuName = "TutorialDefinition")]
public class TutorialDefinition : ScriptableObject
{
    [ListDrawerSettings(DefaultExpandedState = true)]
    public List<TutorialDatum> tutorials;

    public TutorialDatum GetTutorial(int levelIndex)
    {
        return tutorials.Find(t => t.levelIndex == levelIndex);
    }
}

[System.Serializable]
public class TutorialDatum
{
    public int levelIndex;
    [ListDrawerSettings(DefaultExpandedState = true)]
    public List<TutorialStepDatum> steps;
}

[System.Serializable]
public class TutorialStepDatum
{
    public string instruction1;
    public string instruction2;
    public bool enableHand1 = true;
    public bool enableHand2 = true;
    public bool forceSelect1 = false;
    public bool forceSelect2 = false;
    public int boltSender;
    public int boltReceiver;
}
