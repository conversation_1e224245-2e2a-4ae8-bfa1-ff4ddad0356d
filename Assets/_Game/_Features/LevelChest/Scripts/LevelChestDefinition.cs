using System.Collections.Generic;
using OnePuz.Data;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = OnePuz.OPRandom.Random;

namespace OnePuz.Definition
{
    [CreateAssetMenu(menuName = "OnePuz/Definitions/LevelChestDefinition")]
    public class LevelChestDefinition : ScriptableObject
    {
        public int levelToOpenChest = 5;

        [ListDrawerSettings(DefaultExpandedState = true)]
        public LevelChestRewards rewards;

        [SerializeField, ListDrawerSettings(DefaultExpandedState = true)]
        public List<LevelChestRewards> freeRewards;

        [SerializeField, ListDrawerSettings(DefaultExpandedState = true)]
        public List<LevelChestRewards> adsRewards;

        public LevelChestRewards GetRewards(int level)
        {
            if (level <= levelToOpenChest)
            {
                return rewards;
            }

            return IsFreeChest(level) ? freeRewards[Random.Int(0, freeRewards.Count)] : adsRewards[Random.Int(0, adsRewards.Count)];
        }

        public bool IsFreeChest(int level)
        {
            return level <= levelToOpenChest || (level / 5) % 2 != 0;
        }

        [System.Serializable]
        public class LevelChestRewards
        {
            public List<RewardData> rewards;
        }
    }
}