using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using _FeatureHub.Attributes.Core;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Shop.Definition;
using OnePuz.UI;
using PrimeTween;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.Shop.UI
{
    public class UIShopItem : MonoBehaviour
    {
        public delegate void OnPurchasedSuccessfully(UIShopItem item, ShopItemData data);

        public OnPurchasedSuccessfully onPurchasedSuccessfully;

        [Header("Info")] [SerializeField, ReferenceValue("Icon")]
        private Image _iconImage;

        [SerializeField, ReferenceValue("LabelQuantity")]
        private TMP_Text _quantityText;


        [<PERSON>er("Button"), ReferenceValue()] [SerializeField]
        private Button _buyButton;

        [Header("Price")] [SerializeField, ReferenceValue("LabelPrice")]
        private TMP_Text _priceText;

        [SerializeField, ReferenceValue("oldPrice")]
        private TMP_Text _oldPriceText;


        [Header("Rewards")] [SerializeField] private List<UIReward> _rewardItems;

        [SerializeField, ReadOnly] private ShopItemData _data;

        private bool _isInitialized = false;

        public void Init(ShopItemData data)
        {
            _data = data;

            onPurchasedSuccessfully = null;

            _isInitialized = false;

            if (_iconImage)
                _iconImage.sprite = data.icon;

            if (_quantityText)
                _quantityText.text = data.rewards.Count > 0 ? data.rewards.First().Quantity.ToString() : string.Empty;


            _priceText.gameObject.SetActive(true);
            switch (data.priceType)
            {
                case CurrencyType.COIN:
                case CurrencyType.GEM:
                    _priceText.text = data.price.ToString();
                    break;
                case CurrencyType.MONEY:
                    if (Core.IAP.HasInitialized)
                    {
                        var price = Core.IAP.GetLocalizedPrice(data.id);
                        var isoCode = Core.IAP.GetIsoCurrencyCode(data.id);
                        
                        _priceText.text = Core.IAP.GetLocalizedPriceString(data.id);
                        // _priceText.text = Core.IAP.FormatPrice(price, isoCode, false);

                        if (data.id == Core.Definition.Shop.noAdsBundles.id)
                        {
                            if (_oldPriceText)
                            {
                                _oldPriceText.text = $"{Core.IAP.FormatPrice(price, isoCode, true)}";
                                _priceText.text = Core.IAP.FormatPrice(price, isoCode, false);
                            }
                        }
                    }
                    else
                    {
                        _priceText.gameObject.SetActive(false);
                    }

                    break;
                case CurrencyType.REWARDED_VIDEO:
                    _priceText.text = "FREE";
                    break;
                default:
                    _priceText.text = data.price.ToString();
                    break;
            }

            var itemIndex = 0;
            foreach (var datum in _data.rewards.Where(datum => datum.RewardType != RewardType.NO_ADS)
                         .TakeWhile(_ => itemIndex <= _rewardItems.Count - 1))
            {
                if (datum.RewardType == RewardType.CURRENCY && datum.CurrencyType == CurrencyType.COIN)
                {
                    _rewardItems[itemIndex].Init(_data.icon ? _data.icon : Core.GetRewardDefinition(datum).icon,
                        $"{datum.Quantity}");
                }
                else if (datum.RewardType == RewardType.INFINITE_LIVE)
                {
                    var timeSpan = TimeSpan.FromSeconds(datum.Quantity);
                    _rewardItems[itemIndex].Init(Core.GetRewardDefinition(datum).icon,
                        timeSpan.TotalDays >= 1 ? $"{timeSpan.Days}d" : $"{timeSpan.Hours}h");
                }
                else
                {
                    _rewardItems[itemIndex].Init(_data.icon ? _data.icon : Core.GetRewardDefinition(datum).icon,
                        $"x{datum.Quantity}");
                }

                itemIndex++;
            }

            _buyButton.onClick.AddListener(OnClickedBuy);

            _isInitialized = data.priceType != CurrencyType.MONEY || Core.IAP.HasInitialized;
        }

        private void Update()
        {
            if (_isInitialized)
                return;

            if (_data.priceType == CurrencyType.MONEY && Core.IAP.HasInitialized)
            {
                _priceText.gameObject.SetActive(true);
                _priceText.text = Core.IAP.GetLocalizedPriceString(_data.id);
                _isInitialized = true;
            }
        }

        #region Callbacks

        private void OnClickedBuy()
        {
            if (!_isInitialized) return;

            switch (_data.priceType)
            {
                case CurrencyType.COIN:
                {
                    if (DataShortcut.Currency.GetCurrency(CurrencyType.COIN) < _data.price)
                    {
                        UIShortcut.ShowPopup(UIKeys.Panel.POPUP_NOT_ENOUGH_COIN);
                    }
                    else
                    {
                        DataShortcut.Currency.AddCurrency(CurrencyType.COIN, -_data.price);
                        onPurchasedSuccessfully?.Invoke(this, _data);
                    }

                    break;
                }
                case CurrencyType.GEM:
                    break;
                case CurrencyType.REWARDED_VIDEO:
                {
                    if (!Core.Ads.IsRewardedVideoLoaded())
                    {
                        UIShortcut.ShowNoAdsAvailable();
                    }
                    else
                    {
                        Core.Ads.SubscribeRewardedVideoWatchedCallback(OnWatchRewardedVideo);
                        Core.Ads.ShowRewardedVideo();
                    }

                    break;
                }
                case CurrencyType.MONEY:
                {
                    Core.IAP.onPurchased += OnIAPPurchased;
                    if (_data.productId == Core.Definition.Shop.noAdsPack.productId)
                    {
                        if (DataShortcut.Ads.isAdsRemoved)
                        {
                            UIShortcut.ShowPopup(UIKeys.Panel.POPUP_ADS_REMOVED);
                            return;
                        }
                    }

                    Core.IAP.Purchase(_data.productId);
                    break;
                }
            }
        }

        private void OnIAPPurchased(bool succeed, string productid)
        {
            Core.IAP.onPurchased -= OnIAPPurchased;
            if (succeed)
            {
                onPurchasedSuccessfully?.Invoke(this, _data);
            }
        }

        private void OnWatchRewardedVideo(bool succeed)
        {
            Core.Ads.UnsubscribeRewardedVideoWatchedCallback(OnWatchRewardedVideo);
            if (succeed)
            {
                onPurchasedSuccessfully?.Invoke(this, _data);
            }
        }

        private void OnDestroy()
        {
            _buyButton.onClick.RemoveAllListeners();
        }

        #endregion
    }
}