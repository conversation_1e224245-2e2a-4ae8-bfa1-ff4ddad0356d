using System;
using OnePuz.Data;
using OnePuz.Services;

namespace OnePuz.Live
{
    public class DummyLiveService : ILiveService, IServiceLoad
    {
        public TimeSpan TimeLeftToRefill => TimeSpan.Zero;
        public TimeSpan TimeLeftToInfiniteLive => TimeSpan.Zero;
        public bool IsInfinite => true;
        public bool IsRefilling => false;
        public int CurrentLive => 5;
        public bool CanPlay => true;

        public void Load()
        {
            DataShortcut.Live.SetLives(5);
        }
        public void EarnInfiniteLive(long durationInSeconds) { }
        public void EarnLive(int amount = 1) { }
        public void LostLive() { }
    }
}