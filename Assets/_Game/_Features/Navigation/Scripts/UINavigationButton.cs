using OnePuz.Attributes;
using OnePuz.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI.Navigation
{
    public class UINavigationButton : MonoBehaviour
    {
        public SimpleIntCallback OnClick;

        [Header("General")]
        [SerializeField]
        private Button _button;

        [SerializeField]
        private Image _iconImage;

        [SerializeField]
        private TMP_Text _titleText;

        [<PERSON><PERSON>("Animation")]
        [SerializeField, PreAssigned()]
        private Animator _animator;

        private int _index;
        
        private readonly int _tabActive = Animator.StringToHash("TabActive");
        private readonly int _tabInactive = Animator.StringToHash("TabInactive");

        internal void Init(int index, Definition definition)
        {
            _index = index;
            _iconImage.sprite = definition.icon;
            _titleText.text = definition.pageName;

            _button.onClick.RemoveAllListeners();
            _button.onClick.AddListener(() => { OnClick?.Invoke(_index); });

            SetSelected(false, true);
        }

        internal void SetSelected(bool selected, bool instant = false)
        {
            if (instant)
                _animator.SetState(selected ? _tabActive : _tabInactive);
            else
                _animator.PlayManual(selected ? _tabActive : _tabInactive, 0.2f);
        }

        private void OnDestroy()
        {
            if (_button != null)
                _button.onClick.RemoveAllListeners();
        }
    }
}