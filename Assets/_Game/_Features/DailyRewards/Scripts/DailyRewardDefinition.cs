using System;
using System.Collections.Generic;
using OnePuz.Data;
using UnityEngine;

namespace OnePuz.DailyReward.Definitions
{
    [CreateAssetMenu(menuName = "OnePuz/Definitions/DailyRewardDefinition")]
    public class DailyRewardDefinition : ScriptableObject
    {
        public List<DailyRewardDatum> data;
    }
    
    [Serializable]
    public class DailyRewardDatum
    {
        public int day;
        public List<RewardData> rewards;
    }
}