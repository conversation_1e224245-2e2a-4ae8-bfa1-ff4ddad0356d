{"skeleton": {"hash": "6HCjZ3Xuing", "spine": "4.1.24", "x": -563.5, "y": -121, "width": 1127, "height": 242, "images": "../", "audio": ""}, "bones": [{"name": "root"}, {"name": "grow", "parent": "root"}, {"name": "text", "parent": "root", "skin": true}, {"name": "<PERSON>ame", "parent": "root"}, {"name": "text2", "parent": "root", "skin": true}, {"name": "text 3", "parent": "root", "skin": true}, {"name": "frame 2", "parent": "root"}, {"name": "frame 3", "parent": "root"}, {"name": "frame 4", "parent": "root"}, {"name": "frame 5", "parent": "root"}], "slots": [{"name": "Animation/img/frame b1", "bone": "frame 2"}, {"name": "Animation/img/frame b3", "bone": "frame 4"}, {"name": "frame green", "bone": "<PERSON>ame", "attachment": "Animation/img/frame blue"}, {"name": "clip1", "bone": "root", "attachment": "clip1"}, {"name": "grow light", "bone": "grow", "attachment": "grow light", "blend": "additive"}, {"name": "Animation/img/frame b2", "bone": "frame 3"}, {"name": "Animation/img/frame b4", "bone": "frame 5"}, {"name": "Wonderful", "bone": "text 3", "attachment": "Animation/img/wonderful text blue"}, {"name": "Wonderful2", "bone": "text 3", "attachment": "Animation/img/wonderful text blue", "blend": "screen"}, {"name": "Crazy", "bone": "text2", "attachment": "Animation/img/crazy text blue"}, {"name": "Crazy2", "bone": "text2", "attachment": "Animation/img/crazy text blue", "blend": "screen"}, {"name": "Awesome", "bone": "text", "attachment": "Animation/img/awesome text blue"}, {"name": "Awesome 2", "bone": "text", "attachment": "Animation/img/awesome text blue", "blend": "screen"}], "skins": [{"name": "default", "attachments": {"Animation/img/frame b1": {"Animation/img/frame b1": {"width": 1080, "height": 345}}, "Animation/img/frame b2": {"Animation/img/frame b2": {"scaleX": 1.0082, "scaleY": 0.9256, "width": 1080, "height": 538}}, "Animation/img/frame b3": {"Animation/img/frame b1": {"width": 1080, "height": 345}}, "Animation/img/frame b4": {"Animation/img/frame b2": {"scaleX": 1.0082, "scaleY": 0.9256, "width": 1080, "height": 538}}, "Awesome": {"Animation/img/awesome text blue": {"width": 792, "height": 138}}, "Awesome 2": {"Animation/img/awesome text blue": {"width": 792, "height": 138}}, "clip1": {"clip1": {"type": "clipping", "end": "grow light", "vertexCount": 4, "vertices": [-561.31, 119.17, 561.13, 118.99, 562.31, -118.71, -561.31, -120.99], "color": "ce3a3aff"}}, "Crazy": {"Animation/img/crazy text blue": {"width": 516, "height": 139}}, "Crazy2": {"Animation/img/crazy text blue": {"width": 516, "height": 139}}, "frame green": {"Animation/img/frame blue": {"width": 1127, "height": 242}}, "grow light": {"grow light": {"scaleX": 1.9271, "scaleY": 1.9271, "width": 75, "height": 194}}, "Wonderful": {"Animation/img/wonderful text blue": {"x": 0.5, "width": 935, "height": 142}}, "Wonderful2": {"Animation/img/wonderful text blue": {"x": 0.5, "width": 935, "height": 142}}}}, {"name": "Awesome", "bones": ["text"]}, {"name": "Crazy", "bones": ["text2"]}, {"name": "Wonderful", "bones": ["text 3"]}], "animations": {"hide": {"slots": {"Awesome": {"attachment": [{}]}, "Awesome 2": {"attachment": [{}]}, "clip1": {"attachment": [{}]}, "Crazy": {"attachment": [{}]}, "Crazy2": {"attachment": [{}]}, "frame green": {"attachment": [{}]}, "grow light": {"attachment": [{}]}, "Wonderful": {"attachment": [{}]}, "Wonderful2": {"attachment": [{}]}}}, "show": {"slots": {"Animation/img/frame b1": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00", "curve": [0.251, 1, 0.416, 1, 0.251, 1, 0.416, 1, 0.251, 1, 0.416, 1, 0.251, 0, 0.416, 1]}, {"time": 0.5667, "color": "ffffffff", "curve": [0.718, 1, 0.849, 1, 0.718, 1, 0.849, 1, 0.718, 1, 0.849, 1, 0.718, 1, 0.849, 0]}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00", "curve": [1.184, 1, 1.349, 1, 1.184, 1, 1.349, 1, 1.184, 1, 1.349, 1, 1.184, 0, 1.349, 1]}, {"time": 1.5, "color": "ffffffff", "curve": [1.651, 1, 1.782, 1, 1.651, 1, 1.782, 1, 1.651, 1, 1.782, 1, 1.651, 1, 1.782, 0]}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9667, "color": "ffffff00", "curve": [2.118, 1, 2.282, 1, 2.118, 1, 2.282, 1, 2.118, 1, 2.282, 1, 2.118, 0, 2.282, 1]}, {"time": 2.4333, "color": "ffffffff", "curve": [2.584, 1, 2.716, 1, 2.584, 1, 2.716, 1, 2.584, 1, 2.716, 1, 2.584, 1, 2.716, 0]}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00", "curve": [3.029, 1, 3.169, 1, 3.029, 1, 3.169, 1, 3.029, 1, 3.169, 1, 3.029, 0, 3.169, 0.73]}, {"time": 3.3, "color": "ffffffef"}], "attachment": [{"time": 0.1, "name": "Animation/img/frame b1"}]}, "Animation/img/frame b2": {"rgba": [{"time": 0.3333, "color": "ffffff00", "curve": [0.478, 1, 0.622, 1, 0.478, 1, 0.622, 1, 0.478, 1, 0.622, 1, 0.478, 0, 0.622, 1]}, {"time": 0.7667, "color": "ffffffff", "curve": [0.9, 1, 1.033, 1, 0.9, 1, 1.033, 1, 0.9, 1, 1.033, 1, 0.9, 1, 1.033, 0]}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [1.378, 1, 1.522, 1, 1.378, 1, 1.522, 1, 1.378, 1, 1.522, 1, 1.378, 0, 1.522, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": [1.8, 1, 1.933, 1, 1.8, 1, 1.933, 1, 1.8, 1, 1.933, 1, 1.8, 1, 1.933, 0]}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00", "curve": [2.278, 1, 2.422, 1, 2.278, 1, 2.422, 1, 2.278, 1, 2.422, 1, 2.278, 0, 2.422, 1]}, {"time": 2.5667, "color": "ffffffff", "curve": [2.7, 1, 2.833, 1, 2.7, 1, 2.833, 1, 2.7, 1, 2.833, 1, 2.7, 1, 2.833, 0]}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00", "curve": [3.122, 1, 3.211, 1, 3.122, 1, 3.211, 1, 3.122, 1, 3.211, 1, 3.122, 0, 3.211, 0.38]}, {"time": 3.3, "color": "ffffffaa"}], "attachment": [{"time": 0.3333, "name": "Animation/img/frame b2"}]}, "Animation/img/frame b3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00", "curve": [0.718, 1, 0.882, 1, 0.718, 1, 0.882, 1, 0.718, 1, 0.882, 1, 0.718, 0, 0.882, 1]}, {"time": 1.0333, "color": "ffffffff", "curve": [1.184, 1, 1.316, 1, 1.184, 1, 1.316, 1, 1.184, 1, 1.316, 1, 1.184, 1, 1.316, 0]}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": [1.651, 1, 1.816, 1, 1.651, 1, 1.816, 1, 1.651, 1, 1.816, 1, 1.651, 0, 1.816, 1]}, {"time": 1.9667, "color": "ffffffff", "curve": [2.118, 1, 2.249, 1, 2.118, 1, 2.249, 1, 2.118, 1, 2.249, 1, 2.118, 1, 2.249, 0]}, {"time": 2.4, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4333, "color": "ffffff00", "curve": [2.584, 1, 2.749, 1, 2.584, 1, 2.749, 1, 2.584, 1, 2.749, 1, 2.584, 0, 2.749, 1]}, {"time": 2.9, "color": "ffffffff", "curve": [3.04, 1, 3.163, 1, 3.04, 1, 3.163, 1, 3.04, 1, 3.163, 1, 3.04, 1, 3.163, 0.15]}, {"time": 3.3, "color": "ffffff05"}], "attachment": [{"time": 0.5667, "name": "Animation/img/frame b1"}]}, "Animation/img/frame b4": {"rgba": [{"time": 0.6667, "color": "ffffff00", "curve": [0.811, 1, 0.956, 1, 0.811, 1, 0.956, 1, 0.811, 1, 0.956, 1, 0.811, 0, 0.956, 1]}, {"time": 1.1, "color": "ffffffff", "curve": [1.233, 1, 1.367, 1, 1.233, 1, 1.367, 1, 1.233, 1, 1.367, 1, 1.233, 1, 1.367, 0]}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [1.711, 1, 1.856, 1, 1.711, 1, 1.856, 1, 1.711, 1, 1.856, 1, 1.711, 0, 1.856, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.133, 1, 2.267, 1, 2.133, 1, 2.267, 1, 2.133, 1, 2.267, 1, 2.133, 1, 2.267, 0]}, {"time": 2.4, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4667, "color": "ffffff00", "curve": [2.611, 1, 2.756, 1, 2.611, 1, 2.756, 1, 2.611, 1, 2.756, 1, 2.611, 0, 2.756, 1]}, {"time": 2.9, "color": "ffffffff", "curve": [3.033, 1, 3.167, 1, 3.033, 1, 3.167, 1, 3.033, 1, 3.167, 1, 3.033, 1, 3.167, 0]}, {"time": 3.3, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "Animation/img/frame b2"}]}, "Awesome": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}]}, "Awesome 2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 0, 0.756, 1]}, {"time": 0.8, "color": "ffffffff", "curve": [0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 0]}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00", "curve": [1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 0, 1.756, 1]}, {"time": 1.8, "color": "ffffffff", "curve": [1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 0]}, {"time": 1.9333, "color": "ffffff00"}]}, "Crazy": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": [0.342, 1, 0.358, 1, 0.342, 1, 0.358, 1, 0.342, 1, 0.358, 1, 0.342, 0, 0.358, 1]}, {"time": 0.3667, "color": "ffffffff"}]}, "Crazy2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 0.33, 0.756, 1]}, {"time": 0.8, "color": "fffffffe", "curve": [0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 0.33]}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00", "curve": [1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 0.33, 1.756, 1]}, {"time": 1.8, "color": "fffffffe", "curve": [1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 0.33]}, {"time": 1.9333, "color": "ffffff00"}]}, "frame green": {"rgba": [{"color": "ffffff00", "curve": [0.025, 1, 0.075, 1, 0.025, 1, 0.075, 1, 0.025, 1, 0.075, 1, 0.025, 0, 0.075, 1]}, {"time": 0.1, "color": "ffffffff"}]}, "grow light": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.208, 1, 0.292, 1, 0.208, 1, 0.292, 1, 0.208, 1, 0.292, 1, 0.208, 0, 0.292, 0.34]}, {"time": 0.3333, "color": "ffffff57", "curve": "stepped"}, {"time": 0.5, "color": "ffffff57", "curve": [0.542, 1, 0.625, 1, 0.542, 1, 0.625, 1, 0.542, 1, 0.625, 1, 0.542, 0.34, 0.625, 0]}, {"time": 0.6667, "color": "ffffff00"}]}, "Wonderful": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": [0.342, 1, 0.358, 1, 0.342, 1, 0.358, 1, 0.342, 1, 0.358, 1, 0.342, 0, 0.358, 1]}, {"time": 0.3667, "color": "ffffffff"}]}, "Wonderful2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 0.33, 0.756, 1]}, {"time": 0.8, "color": "ffffffff", "curve": [0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 0.33]}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00", "curve": [1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 0.33, 1.756, 1]}, {"time": 1.8, "color": "ffffffff", "curve": [1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 0.33]}, {"time": 1.9333, "color": "ffffff00"}]}}, "bones": {"grow": {"translate": [{"time": 0.1667, "x": -723, "curve": [0.382, -736.95, 0.667, 357.81, 0.382, -0.06, 0.667, 4.45]}, {"time": 0.6667, "x": 730.5, "y": 5.99}], "scale": [{"time": 0.1667, "x": 1.56}], "shear": [{"time": 0.1667, "y": 26.46}]}, "text": {"scale": [{"time": 0.3333, "x": 1.155, "y": 1.078, "curve": [0.358, 1.155, 0.408, 1, 0.358, 1.078, 0.408, 1]}, {"time": 0.4333, "curve": [0.458, 1, 0.508, 1.039, 0.458, 1, 0.508, 1.144]}, {"time": 0.5333, "x": 1.039, "y": 1.144, "curve": [0.55, 1.039, 0.583, 1, 0.55, 1.144, 0.583, 1]}, {"time": 0.6}]}, "Frame": {"scale": [{"y": 0.531, "curve": [0.033, 1, 0.067, 1, 0.033, 0.531, 0.067, 1.192]}, {"time": 0.1, "y": 1.192}, {"time": 0.1667, "y": 0.815}, {"time": 0.2333}]}, "text2": {"scale": [{"time": 0.3333, "x": 1.155, "y": 1.078, "curve": [0.358, 1.155, 0.408, 1, 0.358, 1.078, 0.408, 1]}, {"time": 0.4333, "curve": [0.458, 1, 0.508, 1.039, 0.458, 1, 0.508, 1.144]}, {"time": 0.5333, "x": 1.039, "y": 1.144, "curve": [0.542, 1.039, 0.558, 1, 0.542, 1.144, 0.558, 1]}, {"time": 0.5667}]}, "text 3": {"scale": [{"time": 0.3333, "x": 1.155, "y": 1.078, "curve": [0.358, 1.155, 0.408, 1, 0.358, 1.078, 0.408, 1]}, {"time": 0.4333, "curve": [0.458, 1, 0.508, 1.039, 0.458, 1, 0.508, 1.144]}, {"time": 0.5333, "x": 1.039, "y": 1.144, "curve": [0.542, 1.039, 0.558, 1, 0.542, 1.144, 0.558, 1]}, {"time": 0.5667}]}, "frame 2": {"scale": [{"y": 0.762, "curve": "stepped"}, {"time": 0.1, "y": 0.762, "curve": [0.251, 1, 0.849, 1, 0.251, 0.762, 0.849, 1]}, {"time": 1, "curve": [1.151, 1, 1.022, 1, 1.151, 1, 1.022, 0.762]}, {"time": 1.0333, "y": 0.762, "curve": [1.184, 1, 1.782, 1, 1.184, 0.762, 1.782, 1]}, {"time": 1.9333, "curve": [2.084, 1, 1.956, 1, 2.084, 1, 1.956, 0.762]}, {"time": 1.9667, "y": 0.762, "curve": [2.118, 1, 2.716, 1, 2.118, 0.762, 2.716, 1]}, {"time": 2.8667, "curve": [2.878, 1, 2.889, 1, 2.878, 1, 2.889, 0.762]}, {"time": 2.9, "y": 0.762, "curve": [2.969, 1, 3.131, 1, 2.969, 0.762, 3.131, 0.812]}, {"time": 3.3, "y": 0.865}]}, "frame 3": {"scale": [{"time": 0.3333, "y": 0.648, "curve": [0.478, 1, 1.022, 1, 0.478, 0.765, 1.022, 1]}, {"time": 1.1667, "curve": [1.3, 1, 1.211, 1, 1.3, 1, 1.211, 0.63]}, {"time": 1.2333, "y": 0.648, "curve": [1.378, 1, 1.922, 1, 1.378, 0.765, 1.922, 1]}, {"time": 2.0667, "curve": [2.2, 1, 2.111, 1, 2.2, 1, 2.111, 0.63]}, {"time": 2.1333, "y": 0.648, "curve": [2.278, 1, 2.822, 1, 2.278, 0.765, 2.822, 1]}, {"time": 2.9667, "curve": [2.989, 1, 3.011, 1, 2.989, 1, 3.011, 0.63]}, {"time": 3.0333, "y": 0.648, "curve": [3.084, 1, 3.185, 1, 3.084, 0.689, 3.185, 0.744]}, {"time": 3.3, "y": 0.8}]}, "frame 4": {"scale": [{"y": 0.762, "curve": "stepped"}, {"time": 0.5667, "y": 0.762, "curve": [0.718, 1, 1.316, 1, 0.718, 0.762, 1.316, 1]}, {"time": 1.4667, "curve": [1.618, 1, 1.489, 1, 1.618, 1, 1.489, 0.762]}, {"time": 1.5, "y": 0.762, "curve": [1.651, 1, 2.249, 1, 1.651, 0.762, 2.249, 1]}, {"time": 2.4, "curve": [2.551, 1, 2.422, 1, 2.551, 1, 2.422, 0.762]}, {"time": 2.4333, "y": 0.762, "curve": [2.575, 1, 3.112, 1, 2.575, 0.762, 3.112, 0.971]}, {"time": 3.3, "y": 0.996}]}, "frame 5": {"scale": [{"time": 0.6667, "y": 0.648, "curve": [0.811, 1, 1.356, 1, 0.811, 0.765, 1.356, 1]}, {"time": 1.5, "curve": [1.633, 1, 1.544, 1, 1.633, 1, 1.544, 0.63]}, {"time": 1.5667, "y": 0.648, "curve": [1.711, 1, 2.256, 1, 1.711, 0.765, 2.256, 1]}, {"time": 2.4, "curve": [2.533, 1, 2.444, 1, 2.533, 1, 2.444, 0.63]}, {"time": 2.4667, "y": 0.648, "curve": [2.611, 1, 3.156, 1, 2.611, 0.765, 3.156, 1]}, {"time": 3.3}]}}}, "show loop": {"slots": {"Animation/img/frame b1": {"rgba": [{"color": "ffffffef", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0.94, 0.222, 0]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00", "curve": [0.518, 1, 0.682, 1, 0.518, 1, 0.682, 1, 0.518, 1, 0.682, 1, 0.518, 0, 0.682, 1]}, {"time": 0.8333, "color": "ffffffff", "curve": [0.984, 1, 1.116, 1, 0.984, 1, 1.116, 1, 0.984, 1, 1.116, 1, 0.984, 1, 1.116, 0]}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00", "curve": [1.451, 1, 1.616, 1, 1.451, 1, 1.616, 1, 1.451, 1, 1.616, 1, 1.451, 0, 1.616, 1]}, {"time": 1.7667, "color": "ffffffff", "curve": [1.918, 1, 2.049, 1, 1.918, 1, 2.049, 1, 1.918, 1, 2.049, 1, 1.918, 1, 2.049, 0]}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00", "curve": [2.363, 1, 2.502, 1, 2.363, 1, 2.502, 1, 2.363, 1, 2.502, 1, 2.363, 0, 2.502, 0.73]}, {"time": 2.6333, "color": "ffffffef"}], "attachment": [{"name": "Animation/img/frame b1"}]}, "Animation/img/frame b2": {"rgba": [{"color": "ffffffaa", "curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 1, 0.067, 1, 0.033, 0.78, 0.067, 1]}, {"time": 0.1, "color": "ffffffff", "curve": [0.233, 1, 0.367, 1, 0.233, 1, 0.367, 1, 0.233, 1, 0.367, 1, 0.233, 1, 0.367, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00", "curve": [0.711, 1, 0.856, 1, 0.711, 1, 0.856, 1, 0.711, 1, 0.856, 1, 0.711, 0, 0.856, 1]}, {"time": 1, "color": "ffffffff", "curve": [1.133, 1, 1.267, 1, 1.133, 1, 1.267, 1, 1.133, 1, 1.267, 1, 1.133, 1, 1.267, 0]}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00", "curve": [1.611, 1, 1.756, 1, 1.611, 1, 1.756, 1, 1.611, 1, 1.756, 1, 1.611, 0, 1.756, 1]}, {"time": 1.9, "color": "ffffffff", "curve": [2.033, 1, 2.167, 1, 2.033, 1, 2.167, 1, 2.033, 1, 2.167, 1, 2.033, 1, 2.167, 0]}, {"time": 2.3, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00", "curve": [2.456, 1, 2.545, 1, 2.456, 1, 2.545, 1, 2.456, 1, 2.545, 1, 2.456, 0, 2.545, 0.38]}, {"time": 2.6333, "color": "ffffffaa"}], "attachment": [{"name": "Animation/img/frame b2"}]}, "Animation/img/frame b3": {"rgba": [{"color": "ffffff00", "curve": [0.133, 1, 0.272, 1, 0.133, 1, 0.272, 1, 0.133, 1, 0.272, 1, 0.133, -0.12, 0.272, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.539, 1, 0.661, 1, 0.539, 1, 0.661, 1, 0.539, 1, 0.661, 1, 0.539, 1, 0.661, 0]}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": [0.984, 1, 1.149, 1, 0.984, 1, 1.149, 1, 0.984, 1, 1.149, 1, 0.984, 0, 1.149, 1]}, {"time": 1.3, "color": "ffffffff", "curve": [1.451, 1, 1.582, 1, 1.451, 1, 1.582, 1, 1.451, 1, 1.582, 1, 1.451, 1, 1.582, 0]}, {"time": 1.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7667, "color": "ffffff00", "curve": [1.918, 1, 2.082, 1, 1.918, 1, 2.082, 1, 1.918, 1, 2.082, 1, 1.918, 0, 2.082, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": [2.373, 1, 2.496, 1, 2.373, 1, 2.496, 1, 2.373, 1, 2.496, 1, 2.373, 1, 2.496, 0.15]}, {"time": 2.6333, "color": "ffffff05"}], "attachment": [{"name": "Animation/img/frame b1"}]}, "Animation/img/frame b4": {"rgba": [{"color": "ffffff00", "curve": [0.144, 1, 0.289, 1, 0.144, 1, 0.289, 1, 0.144, 1, 0.289, 1, 0.144, 0, 0.289, 1]}, {"time": 0.4333, "color": "ffffffff", "curve": [0.567, 1, 0.7, 1, 0.567, 1, 0.7, 1, 0.567, 1, 0.7, 1, 0.567, 1, 0.7, 0]}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00", "curve": [1.044, 1, 1.189, 1, 1.044, 1, 1.189, 1, 1.044, 1, 1.189, 1, 1.044, 0, 1.189, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [1.467, 1, 1.6, 1, 1.467, 1, 1.6, 1, 1.467, 1, 1.6, 1, 1.467, 1, 1.6, 0]}, {"time": 1.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8, "color": "ffffff00", "curve": [1.944, 1, 2.089, 1, 1.944, 1, 2.089, 1, 1.944, 1, 2.089, 1, 1.944, 0, 2.089, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": [2.367, 1, 2.5, 1, 2.367, 1, 2.5, 1, 2.367, 1, 2.5, 1, 2.367, 1, 2.5, 0]}, {"time": 2.6333, "color": "ffffff00"}], "attachment": [{"name": "Animation/img/frame b2"}]}, "Awesome 2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 1, 0.756, 1, 0.711, 0, 0.756, 1]}, {"time": 0.8, "color": "ffffffff", "curve": [0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 0]}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00", "curve": [1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 0, 1.756, 1]}, {"time": 1.8, "color": "ffffffff", "curve": [1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 0]}, {"time": 1.9333, "color": "ffffff00"}]}, "Crazy2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.889, 1, 0.756, 1, 0.889, 1, 0.756, 1, 0.889, 1, 0.756, 1, 0.889, 0, 0.756, 1]}, {"time": 0.8, "color": "fffffffe", "curve": [0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 0.33]}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00", "curve": [1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 0.33, 1.756, 1]}, {"time": 1.8, "color": "fffffffe", "curve": [1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 0.33]}, {"time": 1.9333, "color": "ffffff00"}]}, "grow light": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.208, 1, 0.292, 1, 0.208, 1, 0.292, 1, 0.208, 1, 0.292, 1, 0.208, 0, 0.292, 0.34]}, {"time": 0.3333, "color": "ffffff57", "curve": "stepped"}, {"time": 0.5, "color": "ffffff57", "curve": [0.542, 1, 0.625, 1, 0.542, 1, 0.625, 1, 0.542, 1, 0.625, 1, 0.542, 0.34, 0.625, 0]}, {"time": 0.6667, "color": "ffffff00"}]}, "Wonderful2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.861, 1, 0.756, 1, 0.861, 1, 0.756, 1, 0.861, 1, 0.756, 1, 0.861, 0, 0.756, 1]}, {"time": 0.8, "color": "ffffffff", "curve": [0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 1, 0.844, 1, 0.889, 0.33]}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00", "curve": [1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 1, 1.756, 1, 1.711, 0.33, 1.756, 1]}, {"time": 1.8, "color": "ffffffff", "curve": [1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 1, 1.844, 1, 1.889, 0.33]}, {"time": 1.9333, "color": "ffffff00"}]}}, "bones": {"grow": {"translate": [{"time": 0.1667, "x": -723, "curve": [0.382, -736.95, 0.667, 357.81, 0.382, -0.06, 0.667, 4.45]}, {"time": 0.6667, "x": 730.5, "y": 5.99}], "scale": [{"time": 0.1667, "x": 1.56}], "shear": [{"time": 0.1667, "y": 26.46}]}, "frame 2": {"scale": [{"y": 0.865, "curve": [0.111, 1, 0.274, 1, 0.111, 0.901, 0.274, 1]}, {"time": 0.3333, "curve": [0.484, 1, 0.356, 1, 0.484, 1, 0.356, 0.762]}, {"time": 0.3667, "y": 0.762, "curve": [0.518, 1, 1.116, 1, 0.518, 0.762, 1.116, 1]}, {"time": 1.2667, "curve": [1.418, 1, 1.289, 1, 1.418, 1, 1.289, 0.762]}, {"time": 1.3, "y": 0.762, "curve": [1.451, 1, 2.049, 1, 1.451, 0.762, 2.049, 1]}, {"time": 2.2, "curve": [2.211, 1, 2.222, 1, 2.211, 1, 2.222, 0.762]}, {"time": 2.2333, "y": 0.762, "curve": [2.302, 1, 2.464, 1, 2.302, 0.762, 2.464, 0.812]}, {"time": 2.6333, "y": 0.865}]}, "frame 3": {"scale": [{"y": 0.8, "curve": [0.167, 1, 0.416, 1, 0.167, 0.88, 0.416, 1]}, {"time": 0.5, "curve": [0.633, 1, 0.544, 1, 0.633, 1, 0.544, 0.63]}, {"time": 0.5667, "y": 0.648, "curve": [0.711, 1, 1.256, 1, 0.711, 0.765, 1.256, 1]}, {"time": 1.4, "curve": [1.533, 1, 1.444, 1, 1.533, 1, 1.444, 0.63]}, {"time": 1.4667, "y": 0.648, "curve": [1.611, 1, 2.156, 1, 1.611, 0.765, 2.156, 1]}, {"time": 2.3, "curve": [2.322, 1, 2.344, 1, 2.322, 1, 2.344, 0.63]}, {"time": 2.3667, "y": 0.648, "curve": [2.418, 1, 2.519, 1, 2.418, 0.689, 2.519, 0.744]}, {"time": 2.6333, "y": 0.8}]}, "frame 4": {"scale": [{"y": 0.762, "curve": [0.151, 1, 0.673, 1, 0.151, 0.762, 0.673, 1]}, {"time": 0.8, "curve": [0.951, 1, 0.822, 1, 0.951, 1, 0.822, 0.762]}, {"time": 0.8333, "y": 0.762, "curve": [0.984, 1, 1.582, 1, 0.984, 0.762, 1.582, 1]}, {"time": 1.7333, "curve": [1.884, 1, 1.756, 1, 1.884, 1, 1.756, 0.762]}, {"time": 1.7667, "y": 0.762, "curve": [1.909, 1, 2.446, 1, 1.909, 0.762, 2.446, 0.971]}, {"time": 2.6333, "y": 0.996}]}, "frame 5": {"scale": [{"y": 0.648, "curve": [0.144, 1, 0.556, 1, 0.144, 0.765, 0.556, 1]}, {"time": 0.8333, "curve": [0.967, 1, 0.878, 1, 0.967, 1, 0.878, 0.63]}, {"time": 0.9, "y": 0.648, "curve": [1.044, 1, 1.589, 1, 1.044, 0.765, 1.589, 1]}, {"time": 1.7333, "curve": [1.867, 1, 1.778, 1, 1.867, 1, 1.778, 0.63]}, {"time": 1.8, "y": 0.648, "curve": [1.944, 1, 2.489, 1, 1.944, 0.765, 2.489, 1]}, {"time": 2.6333}]}}}}}