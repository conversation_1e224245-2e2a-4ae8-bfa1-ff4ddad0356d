{"name": "Kamgam.SandGame.Editor", "rootNamespace": "", "references": ["Unity.Mathematics", "Unity.Burst", "Unity.Entities", "Unity.Collections", "Unity.Addressables", "Unity.Addressables.Editor", "Unity.ResourceManager", "Kamgam.SandGame", "Kamgam.SandGame.PreSetup.Editor"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}