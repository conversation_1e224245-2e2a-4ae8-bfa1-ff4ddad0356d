{"name": "Tests", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll", "NSubstitute.dll", "Castle.Core.dll", "System.Threading.Tasks.Extensions.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}