using System.Collections.Generic;
// using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

namespace _AddOn.ToolbarExtender.Editor
{
    [CreateAssetMenu(fileName = "PresetSceneItems", menuName = "Scriptable/Create Preset Scenes", order = -1)]
    public class PresetSceneItems : ScriptableObject
    {
        // [ReadOnly]
        // public string title = "Scenes";
        // public string title1 = "Scenes1";
        // [HorizontalGroup]
        public List<SceneAsset> leftScenes;
        // [HorizontalGroup]
        public List<SceneAsset> rightScenes;
    }
}