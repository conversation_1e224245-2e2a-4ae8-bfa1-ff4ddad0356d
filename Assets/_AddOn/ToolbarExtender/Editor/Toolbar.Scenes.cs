using System.IO;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using static _AddOn.ToolbarExtender.Editor.ToolbarStyles;

namespace _AddOn.ToolbarExtender.Editor
{
	internal static class ToolbarStyles
	{
		public static readonly GUIStyle commandButtonStyle;
		public static readonly GUIStyle borderButtonStyle;

		static ToolbarStyles()
		{
			commandButtonStyle = new GUIStyle(GUI.skin.button)
			{
				fontSize = 12,
				alignment = TextAnchor.MiddleCenter,
				fontStyle = FontStyle.Bold,
				fixedHeight = 20,
				padding = { left = 10, right = 10 },
				stretchHeight = true
			};


			borderButtonStyle = new GUIStyle(EditorStyles.miniButtonLeft)
			{
				fixedHeight = 20f,
				padding = { top = 2, bottom = 2 }
			};
		}
	}

	[InitializeOnLoad]
	public class SceneShortcut
	{
		private static readonly PresetSceneItems m_PresetScenes;
		private static bool m_IsScriptModified;
		private static readonly FileSystemWatcher m_Watcher;

		static SceneShortcut()
		{
			m_IsScriptModified = false;
			m_Watcher = new FileSystemWatcher(Application.dataPath, "*.cs");
			m_Watcher.NotifyFilter = NotifyFilters.LastWrite;
			m_Watcher.EnableRaisingEvents = true;
			m_Watcher.IncludeSubdirectories = true;
			m_Watcher.Changed += HandleScriptChanged;
			m_Watcher.Created += HandleScriptChanged;
			
			m_PresetScenes = Resources.Load<PresetSceneItems>("PresetSceneItems");

			ToolbarExtender.LeftToolbarGUI.Add(OnLeftToolbarGUI);
			ToolbarExtender.RightToolbarGUI.Add(OnRightToolbarGUI);
			ToolbarExtender.RightToolbarGUI.Add(OnScriptModifiedDetectorButton);
		}
		
		private static void HandleScriptChanged(object sender, FileSystemEventArgs e)
		{
			if(m_IsScriptModified)
				return;
			m_IsScriptModified = true;
			ToolbarCallback.SetDirty();
		}

	
		
		private static void OnLeftToolbarGUI()
		{
			if (m_PresetScenes == null)
				return;
			
			GUILayout.FlexibleSpace();

			GUI.backgroundColor = 1.35f * Color.gray;
			GUI.contentColor = 0.9f * Color.white;
			foreach (var scene in m_PresetScenes.leftScenes)
			{
				if (scene == null)
					continue;
				if (GUILayout.Button(new GUIContent(scene.name), commandButtonStyle))
				{
					SceneHelper.StartScene(AssetDatabase.GetAssetPath(scene));	
				}
			}

			GUI.backgroundColor = Color.white;
			GUI.contentColor = Color.white;
		}

		private static void OnRightToolbarGUI()
		{
			if (m_PresetScenes == null)
				return;
			
			GUI.backgroundColor = 1.35f * Color.gray;
			GUI.contentColor = 0.9f * Color.white;
			foreach (var scene in m_PresetScenes.rightScenes)
			{
				if (scene == null)
					continue;
				if (GUILayout.Button(new GUIContent(scene.name), commandButtonStyle))
				{
					SceneHelper.StartScene(AssetDatabase.GetAssetPath(scene));	
				}
			}

			GUI.backgroundColor = Color.white;
			GUI.contentColor = Color.white;
			
			GUILayout.FlexibleSpace();
		}

		private static void OnScriptModifiedDetectorButton()
		{
			if(!m_IsScriptModified)
				return;
			
			GUI.backgroundColor = 1.35f * Color.gray;

			if (GUILayout.Button(new GUIContent(EditorGUIUtility.IconContent("console.warnicon").image, 
				    "<color=yellow>Scripts is modified. Click to recompile</color>"), borderButtonStyle))
				AssetDatabase.Refresh();

			GUI.backgroundColor = Color.white;
		}
	}

	internal static class SceneHelper
	{
		public static void StartScene(string scenePath)
		{
			if (EditorApplication.isPlaying)
				EditorApplication.isPlaying = false;

			if (EditorApplication.isPlaying || EditorApplication.isPaused ||
			    EditorApplication.isCompiling || EditorApplication.isPlayingOrWillChangePlaymode)
				return;

			if (EditorSceneManager.SaveCurrentModifiedScenesIfUserWantsTo())
			{
				EditorSceneManager.OpenScene(scenePath);
			}
		}

		[MenuItem("Assets/ToolbarExtender/Add To Left", priority = 0)]
		private static void AddLeftScene()
		{
			var preset = Resources.Load<PresetSceneItems>("PresetSceneItems");
			if (!preset.leftScenes.Contains(Selection.activeObject as SceneAsset) &&
			    !preset.rightScenes.Contains(Selection.activeObject as SceneAsset))
			{
				preset.leftScenes.Add(Selection.activeObject as SceneAsset);
				EditorUtility.SetDirty(preset);
			}

			Resources.UnloadUnusedAssets();
		}

		[MenuItem("Assets/ToolbarExtender/Remove", priority = 1)]
		private static void RemoveScene()
		{
			var preset = Resources.Load<PresetSceneItems>("PresetSceneItems");
			if (preset.leftScenes.Contains(Selection.activeObject as SceneAsset))
				preset.leftScenes.Remove(Selection.activeObject as SceneAsset);
			if (preset.rightScenes.Contains(Selection.activeObject as SceneAsset))
				preset.rightScenes.Remove(Selection.activeObject as SceneAsset);

			EditorUtility.SetDirty(preset);
		}


		[MenuItem("Assets/ToolbarExtender/Add To Right", priority = 0)]
		private static void AddRightScene()
		{
			var preset = Resources.Load<PresetSceneItems>("PresetSceneItems");
			if (!preset.leftScenes.Contains(Selection.activeObject as SceneAsset) &&
			    !preset.rightScenes.Contains(Selection.activeObject as SceneAsset))
			{
				preset.rightScenes.Add(Selection.activeObject as SceneAsset);
				EditorUtility.SetDirty(preset);
			}

			Resources.UnloadUnusedAssets();
		}

		[MenuItem("Assets/ToolbarExtender/Remove", validate = true)]
		private static bool ValidateRemoveScene()
		{
			var preset = Resources.Load<PresetSceneItems>("PresetSceneItems");
			return Selection.activeObject is SceneAsset
			       && (preset.leftScenes.Contains(Selection.activeObject as SceneAsset) ||
			           preset.rightScenes.Contains(Selection.activeObject as SceneAsset));
		}

		[MenuItem("Assets/ToolbarExtender/Add To Left", validate = true)]
		private static bool ValidateAddLeftScene()
		{
			var preset = Resources.Load<PresetSceneItems>("PresetSceneItems");
			return Selection.activeObject is SceneAsset
			       && !preset.leftScenes.Contains(Selection.activeObject as SceneAsset)
			       && !preset.rightScenes.Contains(Selection.activeObject as SceneAsset);
		}

		[MenuItem("Assets/ToolbarExtender/Add To Right", validate = true)]
		private static bool ValidateAddRightScene()
		{
			var preset = Resources.Load<PresetSceneItems>("PresetSceneItems");
			return Selection.activeObject is SceneAsset
			       && !preset.leftScenes.Contains(Selection.activeObject as SceneAsset)
			       && !preset.rightScenes.Contains(Selection.activeObject as SceneAsset);
		}
	}
}