// Toony Colors Pro+Mobile 2
// (c) 2014-2022 <PERSON>
# TCP2_ShaderTemplate_SurfacePBS (Unity 5.5)
#NAME=Surface Standard PBS
#CONFIG=PBS
#INFO=Toony Colors Pro 2 Template  PBS version based on Unity Standard shader, using Unity's Surface shader system
#FEATURES
dd_start	lbl=STANDARD SHADER OPTIONS
---
mult	lbl=PBS Workflow					kw=Metallic|,Specular|PBS_SPECULAR									nohelp
sngl	lbl=Metallic/Specular Map			kw=SPEC_METALLIC_GLOSS_MAP											nohelp		tt=Use a separate texture as specular or metallic map, depending on the workflow
mult	lbl=Smoothness Source				kw=Metallic\Specular Alpha|,Albedo Alpha|SMOOTHNESS_ALBEDO_ALPHA	nohelp
sngl	lbl=Normal/Bump Map					kw=BUMP			hlptop=normal_bump_map_sg
sngl	lbl=Parallax/Height Map				kw=PARALLAX		needs=BUMP	indent	nohelp
sngl	lbl=Occlusion Map					kw=OCCLUSION		nohelp																						tt=Adds an occlusion texture map
keyword	lbl=Color Channel					kw=OCCLUSION_CHNL	nohelp	indent	values=A|a,R|r,G|g,B|b	needs=OCCLUSION		default=2	forceKeyword=true	tt=Which color channel to use from the occlusion texture
sngl	lbl=Emission						kw=EMISSION			nohelp																						tt=Adds an emission texture map and color
sngl	lbl=Detail Texture					kw=DETAIL_TEX
mult	lbl=Detail UV						kw=UV0|,UV1|DETAIL_UV1	nohelp	indent	needs=DETAIL_TEX						tt=Texture coordinates to use for the detail map
mult	lbl=Detail Blending					kw=Multiply Double|DETAIL_BLEND_MULX2,Multiply|DETAIL_BLEND_MUL,Add|DETAIL_BLEND_ADD,Linear|DETAIL_BLEND_LERP		nohelp	indent	needs=DETAIL_TEX	tt=How to blend the detail map
sngl	lbl=Detail Bump/Normal Map			kw=DETAIL_BUMP			nohelp	indent	needs=BUMP,DETAIL_TEX
sngl	lbl=Detail Mask						kw=DETAIL_MASK			nohelp	indent	needs=DETAIL_TEX
---
sngl	lbl=Disable Specular	kw=SPECULAR_OFF			nohelp
sngl	lbl=Disable Fresnel		kw=FRESNEL_OFF			nohelp
dd_end
dd_start	lbl=LIGHTING
---
mult	lbl=Ramp Style						kw=Slider Ramp|,Texture Ramp|TEXTURE_RAMP											tt=Defines the transitioning between dark and lit areas of the model
sngl	lbl=Colors Multipliers				kw=COLOR_MULTIPLIERS											nohelp				tt=Adds multiplier values for highlight and shadow colors to enhance contrast or colors
mult	lbl=Shadow Color Texture			kw=Disabled|,Multiply|SHADOW_COLOR_TEX_MULT,Replace Color|SHADOW_COLOR_TEX_LERP	toggles=SHADOW_COLOR_TEX	nohelp	tt=Use a texture as the color source for shadows
sngl	lbl=Diffuse Tint					kw=DIFFUSE_TINT																		tt=Adds a diffuse tint color, to add some subtle coloring to the diffuse lighting
mult	lbl=Wrapped Lighting				kw=Off|,Half|WRAPPED_DIFFUSE,Custom|WRAP_CUSTOM										tt=Enable wrapped lighting, with different levels of control
---
sngl	lbl=Subsurface Scattering	kw=SUBSURFACE_SCATTERING
mult	lbl=Subsurface Lights	kw=Point\Spot Lights|,Directional Lights|SS_DIR_LIGHTS,All Lights|SS_ALL_LIGHTS	needs=SUBSURFACE_SCATTERING		indent	nohelp		tt=Defines which lights will affect subsurface scattering
mask	lbl=Subsurface Mask		msk=SS_MASK		ch=SS_MASK_CHANNEL	kw=SS_MASK	dispName=Subsurface				needs=SUBSURFACE_SCATTERING		indent	nohelp		tt=Defines where the subsurface scattering effect should apply
sngl	lbl=Subsurface Color			kw=SUBSURFACE_COLOR														needs=SUBSURFACE_SCATTERING		indent	nohelp		tt=Control the color of the subsurface effect (only affecting back lighting)
sngl	lbl=Subsurface Ambient Color	kw=SUBSURFACE_AMB_COLOR													needs=SUBSURFACE_SCATTERING		indent	nohelp		tt=Adds an ambient subsurface color, affecting both front and back lighting
sngl	lbl=Subsurface Light Color		kw=SS_COLOR_FROM_LIGHT													needs=SUBSURFACE_SCATTERING		indent	nohelp		tt=Inherit the subsurface color from the lights
sngl	lbl=Multiplicative				kw=SS_MULTIPLICATIVE													needs=SUBSURFACE_SCATTERING		indent	nohelp		tt=Makes the subsurface scattering effect multiplied to the diffuse color instead of added with it
dd_end
dd_start	lbl=SURFACE
---
mask	lbl=Albedo Color Mask		msk=COLORMASK	ch=COLORMASK_CHANNEL	kw=COLORMASK	dispName=Color		hlptop=Color Mask
#sngl	lbl=Separate Color			kw=COLORMASK_SEPARATE					needs=COLORMASK						indent				nohelp			tt=Use a separate masked color (if disabled, will use the main Color property)
mult	lbl=Blending				kw=Multiply|,Replace Color|COLORMASK_REPLACE,Add|COLORMASK_ADD			needs=COLORMASK	indent	nohelp	tt=Blending mode for the masked color
---
sngl	lbl=HSV Controls			kw=HSV_CONTROLS																					nohelp			tt=Adds Hue, Saturation, Value controls to the main texture
mask	lbl=HSV Mask				msk=HSV_MASK	ch=HSV_MASK_CHANNEL		kw=HSV_MASK		dispName=HSV	needs=HSV_CONTROLS		nohelp			tt=Masks the HSV control
---
sngl	lbl=Vertex Colors				kw=VCOLORS					tt=Multiplies the color with vertex colors
sngl	lbl=Gamma to Linear Space		kw=VCOLORS_LINEAR	needs=VCOLORS	indent	nohelp		tt=Converts the vertex color from gamma to linear space (when linear color space is enabled)
---
mult	lbl=Texture Blending			kw=Off|,Vertex Colors|TEXBLEND_VCOLORS,Texture Map|TEXBLEND_MAP	toggles=TEXTURE_BLENDING				tt=Enable texture blending
#mult	lbl=Texture Blending			kw=Off|,Vertex Colors|TEXBLEND_VCOLORS,Texture Map|TEXBLEND_MAP,Unity Terrain Splatmap|TEXBLEND_UNITY_SPLATMAP	toggles=TEXTURE_BLENDING				tt=Enable texture blending
mult	lbl=Blend Method				kw=Linear|TEXBLEND_LINEAR,Linear (Additive)|TEXBLEND_LINEAR_ADD,Height (Texture Alpha)|TEXBLEND_HEIGHT	nohelp	indent	needs=TEXTURE_BLENDING		tt=Defines how to blend textures (see documentation)
sngl	lbl=Enhance Blend Contrast		kw=TEXBLEND_NORMALIZE		nohelp	indent	needs=TEXTURE_BLENDING	excl=TEXBLEND_LINEAR_ADD	tt=Enhance contrast between colors when the blend channel is not 0.\nSee documentation for more info.
float	lbl=Height Contrast				kw=TEXBLEND_HEIGHT_CONTRAST		default=2.5	needs=TEXBLEND_HEIGHT	nohelp	indent		tt=Adjust the contrast for height-based blending (default: 2.5)
warning	msgType=info					needs=TEXBLEND_HEIGHT		lbl=The order in which textures are sampled matters when using height blending, because the alpha from the textures is cumulative!
sngl	lbl=Texture 1					kw=BLEND_TEX1	tt=Additional texture blended based on a vertex color channel		nohelp	indent	needs=TEXTURE_BLENDING			half
keyword	lbl=Color Channel				kw=BLEND_TEX1_CHNL	values=R|r,G|g,B|b,A|a		needs=TEXTURE_BLENDING,BLEND_TEX1	nohelp	default=0	forceKeyword=true	inline
sngl	lbl=Non-repeating Tiling		kw=BLEND_TEX1_NOTILE	tt=Non-repeating tiling for texture 1						nohelp	indent	needs=TEXTURE_BLENDING,BLEND_TEX1
sngl	lbl=Texture 2					kw=BLEND_TEX2	tt=Additional texture blended based on a vertex color channel		nohelp	indent	needs=TEXTURE_BLENDING			half
keyword	lbl=Color Channel				kw=BLEND_TEX2_CHNL	values=R|r,G|g,B|b,A|a		needs=TEXTURE_BLENDING,BLEND_TEX2	nohelp	default=1	forceKeyword=true	inline
sngl	lbl=Non-repeating Tiling		kw=BLEND_TEX2_NOTILE	tt=Non-repeating tiling for texture 2						nohelp	indent	needs=TEXTURE_BLENDING,BLEND_TEX2
sngl	lbl=Texture 3					kw=BLEND_TEX3	tt=Additional texture blended based on a vertex color channel		nohelp	indent	needs=TEXTURE_BLENDING			half
keyword	lbl=Color Channel				kw=BLEND_TEX3_CHNL	values=R|r,G|g,B|b,A|a		needs=TEXTURE_BLENDING,BLEND_TEX3	nohelp	default=2	forceKeyword=true	inline
sngl	lbl=Non-repeating Tiling		kw=BLEND_TEX3_NOTILE	tt=Non-repeating tiling for texture 3						nohelp	indent	needs=TEXTURE_BLENDING,BLEND_TEX3
sngl	lbl=Texture 4					kw=BLEND_TEX4	tt=Additional texture blended based on a vertex color channel		nohelp	indent	needs=TEXTURE_BLENDING			half	excl=TEXBLEND_UNITY_SPLATMAP
keyword	lbl=Color Channel				kw=BLEND_TEX4_CHNL	values=R|r,G|g,B|b,A|a		needs=TEXTURE_BLENDING,BLEND_TEX4	nohelp	default=3	forceKeyword=true	inline	excl=TEXBLEND_UNITY_SPLATMAP
sngl	lbl=Non-repeating Tiling		kw=BLEND_TEX4_NOTILE	tt=Non-repeating tiling for texture 4						nohelp	indent	needs=TEXTURE_BLENDING,BLEND_TEX4
sngl	lbl=Normal Map Blending			kw=TEXBLEND_BUMP			toggles=BUMP						needs=TEXTURE_BLENDING		nohelp	indent		tt=Enables texture blending for the normal map as well
sngl	lbl=Specular/Metallic Blending	kw=TEXBLEND_SPEC_METALLIC	toggles=SPEC_METALLIC_GLOSS_MAP		needs=TEXTURE_BLENDING		nohelp	indent		tt=Enables texture blending for the specular/metallic map
---
mult	lbl=Tessellation			kw=Off|,Phong|TESSELLATION_PHONG,Fixed|TESSELLATION_FIXED,Distance Based|TESSELLATION_DIST,Edge Length Based|TESSELLATION_EDGE_LENGTH						nohelp
mult	lbl=Height Source			kw=MainTex Alpha|,Height Map|TESSELLATION_HEIGHT_MAP		needsOr=TESSELLATION_FIXED,TESSELLATION_DIST,TESSELLATION_EDGE_LENGTH		nohelp	indent		tt=Defines the source of the displacement for the tessellation effect
warning	msgType=warning				needsOr=TESSELLATION_PHONG,TESSELLATION_FIXED,TESSELLATION_DIST,TESSELLATION_EDGE_LENGTH		lbl=<b>Tessellation</b> is incompatible with using a custom vertex function.  The shader <b>may not compile properly</b> depending on the used features.  <i>(this is unfortunately a Unity limitation)</i>
warning	msgType=info				needsOr=TESSELLATION_PHONG,TESSELLATION_FIXED,TESSELLATION_DIST,TESSELLATION_EDGE_LENGTH		lbl=Enable the <b>Add Shadow Pass</b> flag to get proper shadow casting and receiving with Tessellation.
#---
#sngl	lbl=Vertex Texture Blending		kw=VCOLORS_BLENDING			tt=Enables 2-way texture blending based on the mesh's vertex color alpha
#sngl	lbl=Normal Map Blending			kw=VCOLORS_BUMP_BLENDING	tt=Enables 2-way texture blending for the normal map as well				needs=VCOLORS_BLENDING	indent
#---
#sngl	lbl=Dissolve Map				kw=DISSOLVE																nohelp					tt=Adds a dissolve texture map with the corresponding slider
#mult	lbl=Color Channel				kw=Alpha|,Red|DSLV_R,Green|DSLV_G,Blue|DSLV_B		needs=DISSOLVE		nohelp	indent		tt=Color channel to use for the dissolve map
#sngl	lbl=Independent UV				kw=DISSOLVE_UV										needs=DISSOLVE		nohelp	indent		tt=Use separate UVs with its own tiling and offset values for the dissolve map (else use the main texture UV)
dd_end
dd_start	lbl=STYLIZATION
---
sngl	lbl=Stylized Specular	kw=SPECULAR_STYLIZED											excl=SPECULAR_OFF		tt=Enables clear delimitation of specular color
sngl	lbl=Stylized Fresnel	kw=FRESNEL_STYLIZED												excl=FRESNEL_OFF		tt=Enables accentuated Fresnel-reflections relative to realtime lights
---
mult	lbl=Textured Threshold			kw=Off|,Main Light|TEXTHR_MAIN,Other Lights|TEXTHR_OTHER,All Lights|TEXTHR_ALL		tt=Adds a textured variation to the highlight/shadow threshold, allowing handpainting like effects for example
mult	lbl=UV Channel					kw=Main Tex|, UV0|TEXTURED_THRESHOLD_UV0,UV1|TEXTURED_THRESHOLD_UV1		needsOr=TEXTHR_MAIN,TEXTHR_OTHER,TEXTHR_ALL	indent	nohelp		tt=Which UV source/channel to use for the threshold texture
---
mult	lbl=Sketch						kw=Off|,Sketch Overlay|SKETCH,Sketch Gradient|SKETCH_GRADIENT							tt=Sketch texture overlay on the shadowed areas\nOverlay: regular texture overlay\nGradient: used for halftone-like effects
mult	lbl=Sketch Blending				kw=Regular|,Color Burn|SKETCH_COLORBURN		needs=SKETCH	nohelp	indent		tt=Defines how to blend the Sketch texture with the model
sngl	lbl=Animated Sketch				kw=SKETCH_ANIM			needsOr=SKETCH,SKETCH_GRADIENT		nohelp	indent		tt=Animates the sketch overlay texture, simulating a hand-drawn animation style
sngl	lbl=Vertex Coords				kw=SKETCH_VERTEX		needsOr=SKETCH,SKETCH_GRADIENT		nohelp	indent		tt=Compute screen coordinates in vertex shader (faster but can cause distortions)\nIf disabled will compute in pixel shader (slower)
sngl	lbl=Disable Obj-Space Offset	kw=NO_SKETCH_OFFSET		needsOr=SKETCH,SKETCH_GRADIENT		nohelp	indent		tt=Prevent the screen-space UVs from being offset based on the object's position
---
#mult	lbl=Outline					kw=Off|,Opaque Outline|OUTLINE,Blended Outline|OUTLINE_BLENDING											tt=Outline around the model
#sngl	lbl=Outline behind model	kw=OUTLINE_BEHIND		needsOr=OUTLINE,OUTLINE_BLENDING								indent	nohelp		tt=If enabled, outline will only show behind model
#sngl	lbl=Depth Pass				kw=OUTLINE_DEPTH		needsOr=OUTLINE,OUTLINE_BLENDING	needs=OUTLINE_BEHIND		indent	nohelp		tt=Adds a depth writing pass for the outline behind model: this can solve issues with sorting and drawing order
mult	lbl=Outline					kw=Off|,Opaque Outline|OUTLINE,Blended Outline|OUTLINE_BLENDING											tt=Outline around the model
sngl	lbl=Legacy Outline			kw=LEGACY_OUTLINE	needsOr=OUTLINE,OUTLINE_BLENDING								indent	nohelp		tt=Legacy behavior of the outline (prior to v2.3.36 that introduced some fixes)
sngl	lbl=HDR Outline Color		kw=HDR_OUTLINE		needsOr=OUTLINE,OUTLINE_BLENDING								indent	nohelp		tt=Makes the outline color HDR, being able to go over 1
mult	lbl=Outline behind model	kw=Off|,Depth Buffer|OUTLINE_BEHIND_DEPTH,Stencil Buffer|OUTLINE_BEHIND_STENCIL		needsOr=OUTLINE,OUTLINE_BLENDING	indent	nohelp		tt=Show outline behind model
sngl	lbl=Depth Pass				kw=OUTLINE_DEPTH	needsOr=OUTLINE,OUTLINE_BLENDING	needs=OUTLINE_BEHIND_DEPTH	indent	nohelp		tt=Adds a depth writing pass for the outline behind model: this can solve issues with sorting and drawing order
space	needs=OUTLINE_BEHIND_DEPTH	needsOr=OUTLINE,OUTLINE_BLENDING
mult	lbl=Outline as fake rim		kw=Off|,Based on Main Directional Light|OUTLINE_FAKE_RIM_DIRLIGHT,Manual Offset|OUTLINE_FAKE_RIM	excl=LEGACY_OUTLINE		needsOr=OUTLINE,OUTLINE_BLENDING	indent	nohelp		tt=Use the outline as a fake crisp rim light
sngl	lbl=Vertex Lighting			kw=OFRD_LIGHTING	needsOr=OUTLINE,OUTLINE_BLENDING	needs=OUTLINE_FAKE_RIM_DIRLIGHT	indent	nohelp		tt=Apply basic vertex lighting to attenuate the fake rim outline color based on the directional light
sngl	lbl=Directional Light Color	kw=OFRD_COLOR		needsOr=OUTLINE,OUTLINE_BLENDING	needs=OUTLINE_FAKE_RIM_DIRLIGHT	indent	nohelp		tt=Multiply the fake rim color with the main directional light's color
space	needs=OUTLINE_FAKE_RIM_DIRLIGHT	needsOr=OUTLINE,OUTLINE_BLENDING
sngl	lbl=Vertex Color Width		kw=OUTLINE_VCOLOR_WIDTH	needsOr=OUTLINE,OUTLINE_BLENDING							indent	nohelp	excl=LEGACY_OUTLINE		tt=Modulate the outline width with a vertex color		half
keyword	lbl=Channel	kw=OVCW_CHNL	values=R|r,G|g,B|b,A|a	needsOr=OUTLINE,OUTLINE_BLENDING	needs=OUTLINE_VCOLOR_WIDTH		nohelp	excl=LEGACY_OUTLINE	default=0	forceKeyword=true								inline
sngl	lbl=Shadow/Depth Pass		kw=OUTLINE_SHADOWCASTER	needsOr=OUTLINE,OUTLINE_BLENDING							indent	nohelp	excl=LEGACY_OUTLINE		tt=Adds a shadow caster pass based on the outline vertices. This will ensure that the cast shadows include the thickness of the outline, and also that the outline is included in the depth texture (e.g. for post effects like depth of field)
dd_end
dd_start	lbl=SPECIAL EFFECTS
---
sngl	lbl=Silhouette Pass				kw=PASS_SILHOUETTE																nohelp				tt=Adds a silhouette pass, to show the object when it is behind obstacles
sngl	lbl=Stencil Mask				kw=SILHOUETTE_STENCIL								needs=PASS_SILHOUETTE		nohelp	indent		tt=Use the Stencil Buffer as a mask for the silhouette, to prevent transparency issues with non-convex meshes or multiple meshes
float	lbl=Stencil Ref					kw=SILHOUETTE_STENCIL_REF	default=1				needs=PASS_SILHOUETTE,SILHOUETTE_STENCIL		nohelp	indent		tt=Reference Stencil value for the mask effect  Value range is [1-255]
dd_end
dd_start	lbl=SHADER STATES/MISC
---
mult	lbl=Culling/Double-sided		kw=Back (default)|,Front|CULL_FRONT,Off (double-sided)|CULL_OFF,User-defined in Material|CULL_OPTION	nohelp	tt=Defines how to cull faces
sngl	lbl=Backface Lighting			kw=USE_VFACE	indent	nohelp		tt=Invert the normals on backfaces for accurate lighting calculation (this may not work properly with shadows and introduce other artifacts)
---
keyword	lbl=Shader Target	kw=SHADER_TARGET	forceKeyword=true	values=2.0 (Old hardware)|2.0,2.5 (Mobile devices)|2.5,3.0 (Recommended default)|3.0,3.5|3.5,4.0|4.0,4.5|4.5,4.6|4.6,5.0|5.0		default=2
warning	msgType=info		lbl=Use Shader Target 2.5 for maximum compatibility across mobile devices    Increase the number if the shader fails to compile (not enough instructions or interpolators)
dd_end
dd_start	lbl= SURFACE SHADER FLAGS
---
flag	lbl=Add Shadow Passes			kw=addshadow													tt=Force the shader to have the Shadow Caster and Collector passes.\nCan help if shadows don't work properly with the shader
flag	lbl=Full Forward Shadows		kw=fullforwardshadows											tt=Enable support for all shadow types in Forward rendering path
flag	lbl=Disable Shadows				kw=noshadow					toggles=NO_SHADOW					tt=Disables all shadow receiving support in this shader
flag	lbl=Disable Fog					kw=nofog														tt=Disables Unity Fog support.\nCan help if you run out of vertex interpolators and don't need fog.
flag	lbl=Disable Lightmaps			kw=nolightmap				toggles=NO_LIGHTMAP					tt=Disables all lightmapping support in this shader.\nCan help if you run out of vertex interpolators and don't need lightmaps.
flag	lbl=Disable Ambient Lighting	kw=noambient		excl=DIRAMBIENT,CUBE_AMBIENT,OCCLUSION		tt=Disable ambient lighting
flag	lbl=Disable Vertex Lighting		kw=novertexlights												tt=Disable vertex lights and spherical harmonics (light probes)
sngl	lbl=Disable Dynamic Batching	kw=DISABLE_BATCHING		nohelp									tt=Disable dynamic batching support for this shader.  Can help if dynamic batching causes UV or vertex displacement issues among water planes for example.
space	space=6
header	lbl=Mobile-Friendly
flag	lbl=One Directional Light		kw=noforwardadd													tt=Use additive lights as vertex lights.\nRecommended for old mobile devices
flag	lbl=Vertex View Dir				kw=interpolateview												tt=Calculate view direction per-vertex instead of per-pixel.\nRecommended for old mobile devices
flag	lbl=Half as View				kw=halfasview													tt=Pass half-direction vector into the lighting function instead of view-direction.\nFaster but inaccurate.
dd_end
dd_start	lbl=THIRD PARTY PLUGINS
---
mult	lbl=VertExmotion Support			kw=Off|,Position|VERTEXMOTION_SIMPLE,Position+Normal|VERTEXMOTION_NORMAL	toggles=VERTEXMOTION	nohelp		tt=Adds support for VertExmotion
dd_end
#END

#KEYWORDS

#View Dir
/// IF (BUMP && PARALLAX)
enable_kw	USE_VIEWDIR
///

#Textured Threshold
/// IF TEXTHR_MAIN || TEXTHR_OTHER || TEXTHR_ALL
enable_kw	TEXTURED_THRESHOLD
/// ELSE
disable_kw	TEXTURED_THRESHOLD
///

#Vertex Color
/// IF VCOLORS_MASK || VCOLORS || TEXBLEND_VCOLORS || VERTEXMOTION
enable_kw	USE_VERTEX_COLORS
///

#Screen Space Coordinates
/// IF (SKETCH || SKETCH_GRADIENT) && !SKETCH_VERTEX
enable_kw	USE_SCREENSPACE_COORDS_FRAGMENT
///

/// IF ((SKETCH || SKETCH_GRADIENT) && SKETCH_VERTEX) || USE_SCREENSPACE_COORDS_FRAGMENT
enable_kw	USE_SCREENSPACE_COORDS_VERTEX
///

#NoTile
/// IF MAINTEX_NOTILE || BLEND_TEX1_NOTILE || BLEND_TEX2_NOTILE || BLEND_TEX3_NOTILE || BLEND_TEX4_NOTILE
enable_kw		NOTILE_TEXTURES
/// ELSE
disable_kw		NOTILE_TEXTURES
///

#Tessellation
/// IF TESSELLATION_PHONG
enable_kw	TESSELLATION
enable_flag	vertex:dispNone
enable_flag	tessellate:tessEdge
enable_flag	tessphong:_Phong
/// ELIF TESSELLATION_FIXED
enable_kw	TESSELLATION
enable_flag	vertex:disp
enable_flag	tessellate:tessFixed
/// ELIF TESSELLATION_DIST
enable_kw	TESSELLATION
enable_flag	vertex:disp
enable_flag	tessellate:tessDistance
/// ELIF TESSELLATION_EDGE_LENGTH
enable_kw	TESSELLATION
enable_flag	vertex:disp
enable_flag	tessellate:tessEdge
///

#Vertex function
/// IF VERTEX_FUNC || USE_SCREENSPACE_COORDS_VERTEX || VERTEXMOTION
enable_kw		VERTEX_FUNC
enable_flag		vertex:vert
///
er
#END

Shader "@%SHADER_NAME%@"
{
	Properties
	{
		_Color("Color", Color) = (1,1,1,1)
		_MainTex("Albedo", 2D) = "white" {}
/// IF TEXTURE_BLENDING

	/// IF TEXBLEND_VCOLORS
		[Header(Texture Blending (Vertex Colors))]
	/// ELIF TEXBLEND_MAP
		[Header(Texture Blending (Texture Map))]
		_TexBlendMap ("Texture Blending Map", 2D) = "black" {}
		[Space]
	/// ELIF TEXBLEND_UNITY_SPLATMAP
		[Header(Texture Blending (Unity Terrain))]
	///
	/// IF BLEND_TEX1
		_BlendTex1 ("Texture 1 (@%BLEND_TEX1_CHNL%@)", 2D) = "white" {}
	///
	/// IF BLEND_TEX2
		_BlendTex2 ("Texture 2 (@%BLEND_TEX2_CHNL%@)", 2D) = "white" {}
	///
	/// IF BLEND_TEX3
		_BlendTex3 ("Texture 3 (@%BLEND_TEX3_CHNL%@)", 2D) = "white" {}
	///
	/// IF BLEND_TEX4
		_BlendTex4 ("Texture 4 (@%BLEND_TEX4_CHNL%@)", 2D) = "white" {}
	///
	/// IF TEXBLEND_NORMALIZE
		[PowerSlider(4.0)] _BlendContrast ("Blending Contrast", Range(1,4)) = 1
	///
	/// IF TEXBLEND_HEIGHT
		[Header(Height Blending Parameters)]
		[TCP2Vector4Floats(R,G,B,A,0.001,2,0.001,2,0.001,2,0.001,2)] _VColorBlendSmooth ("Height Smoothing", Vector) = (0.25,0.25,0.25,0.25)
		[TCP2Vector4Floats(R,G,B,A)] _VColorBlendOffset ("Height Offset", Vector) = (0,0,0,0)
		[TCP2HelpBox(Info,Height will be taken from each texture alpha channel.  No alpha in the texture will result in linear blending.)]
	///
///

		_Cutoff("Alpha Cutoff", Range(0.0, 1.0)) = 0.5

		_Glossiness("Smoothness", Range(0.0, 1.0)) = 0.5
/// IF PBS_SPECULAR
		_GlossMapScale("Smoothness Factor", Range(0.0, 1.0)) = 1.0

		_SpecColor("Specular", Color) = (0.2,0.2,0.2)
	/// IF SPEC_METALLIC_GLOSS_MAP
		[NoScaleOffset] _SpecGlossMap("Specular", 2D) = "black" {}
		/// IF TEXTURE_BLENDING && TEXBLEND_SPEC_METALLIC
			/// IF BLEND_TEX1
		[NoScaleOffset] _SpecGlossMap1("Specular 1 (@%BLEND_TEX1_CHNL%@)", 2D) = "black" {}
			///
			/// IF BLEND_TEX2
		[NoScaleOffset] _SpecGlossMap2("Specular 2 (@%BLEND_TEX2_CHNL%@)", 2D) = "black" {}
			///
			/// IF BLEND_TEX3
		[NoScaleOffset] _SpecGlossMap3("Specular 3 (@%BLEND_TEX3_CHNL%@)", 2D) = "black" {}
			///
			/// IF BLEND_TEX4
		[NoScaleOffset] _SpecGlossMap4("Specular 4 (@%BLEND_TEX4_CHNL%@)", 2D) = "black" {}
			///
		///
	///
/// ELSE
		_GlossMapScale("Smoothness Scale", Range(0.0, 1.0)) = 1.0

		[Gamma] _Metallic("Metallic", Range(0.0, 1.0)) = 0.0
	/// IF SPEC_METALLIC_GLOSS_MAP
		[NoScaleOffset] _MetallicGlossMap("Metallic", 2D) = "black" {}
		/// IF TEXTURE_BLENDING && TEXBLEND_SPEC_METALLIC
			/// IF BLEND_TEX1
		[NoScaleOffset] _MetallicGlossMap1("Metallic 1 (@%BLEND_TEX1_CHNL%@)", 2D) = "black" {}
			///
			/// IF BLEND_TEX2
		[NoScaleOffset] _MetallicGlossMap2("Metallic 2 (@%BLEND_TEX2_CHNL%@)", 2D) = "black" {}
			///
			/// IF BLEND_TEX3
		[NoScaleOffset] _MetallicGlossMap3("Metallic 3 (@%BLEND_TEX3_CHNL%@)", 2D) = "black" {}
			///
			/// IF BLEND_TEX4
		[NoScaleOffset] _MetallicGlossMap4("Metallic 4 (@%BLEND_TEX4_CHNL%@)", 2D) = "black" {}
			///
		///
	///
///

		// [ToggleOff] _SpecularHighlights("Specular Highlights", Float) = 1.0
		// [ToggleOff] _GlossyReflections("Glossy Reflections", Float) = 1.0

/// IF BUMP
		_BumpScale("Scale", Float) = 1.0
		_BumpMap("Normal Map", 2D) = "bump" {}
	/// IF TEXTURE_BLENDING && TEXBLEND_BUMP

		/// IF BLEND_TEX1
		[NoScaleOffset] _BumpMap1 ("Normal map 1", 2D) = "bump" {}
		///
		/// IF BLEND_TEX2
		[NoScaleOffset] _BumpMap2 ("Normal map 2", 2D) = "bump" {}
		///
		/// IF BLEND_TEX3
		[NoScaleOffset] _BumpMap3 ("Normal map 3", 2D) = "bump" {}
		///
		/// IF BLEND_TEX4
		[NoScaleOffset] _BumpMap4 ("Normal map 4", 2D) = "bump" {}
		///
	///

	/// IF PARALLAX || ((TESSELLATION_FIXED || TESSELLATION_DIST) && TESSELLATION_HEIGHT_MAP)
		_Parallax("Height Scale", Range (0.005, 0.08)) = 0.02
		_ParallaxMap("Height Map", 2D) = "black" {}

	///
///
/// IF TESSELLATION_PHONG

		[Header(Tessellation)]
		_EdgeLength ("Edge length", Range(2,50)) = 5
		_Phong ("Phong Strengh", Range(0,1)) = 0.5
/// ELIF TESSELLATION_FIXED || TESSELLATION_DIST || TESSELLATION_EDGE_LENGTH

		[Header(Tessellation)]
	/// IF !BUMP && TESSELLATION_HEIGHT_MAP
		[NoScaleOffset] _ParallaxMap ("Heightmap (Alpha)", 2D) = "black" {}
	///
		_Displacement ("Displacement", Float) = 0.3
		_Tess ("Tessellation", Range(1,32)) = 4
	/// IF TESSELLATION_DIST
		_TessDistMin ("Min Dist", Float) = 10
		_TessDistMax ("Max Dist", Float) = 25
	///
	/// IF TESSELLATION_EDGE_LENGTH
		_EdgeLength ("Edge length", Float) = 15
	///
///
/// IF OCCLUSION
		_OcclusionStrength("Strength", Range(0.0, 1.0)) = 1.0
		_OcclusionMap("Occlusion", 2D) = "white" {}

///
/// IF EMISSION
		_EmissionColor("Color", Color) = (0,0,0)
		_EmissionMap("Emission", 2D) = "white" {}

///
/// IF DETAIL_TEX

		_DetailAlbedoMap("Detail Albedo x2", 2D) = "grey" {}
	/// IF BUMP && DETAIL_BUMP
		_DetailNormalMapScale("Scale", Float) = 1.0
		_DetailNormalMap("Normal Map", 2D) = "bump" {}
	///
	/// IF DETAIL_MASK
		_DetailMask("Detail Mask", 2D) = "white" {}
	///
		//[Enum(UV0,0,UV1,1)] _UVSec ("UV Set for secondary textures", Float) = 0
///
/// IF NOTILE_TEXTURES

		[Header(Misc)]
		[NoScaleOffset] _NoTileNoiseTex ("Non-repeat tiling Noise", 2D) = "white" {}
///

		// Blending state
		[HideInInspector] _Mode("__mode", Float) = 0.0
		[HideInInspector] _SrcBlend("__src", Float) = 1.0
		[HideInInspector] _DstBlend("__dst", Float) = 0.0
		[HideInInspector] _ZWrite("__zw", Float) = 1.0

		//TOONY COLORS PRO 2 ----------------------------------------------------------------
		_HColor("Highlight Color", Color) = (0.785,0.785,0.785,1.0)
		_SColor("Shadow Color", Color) = (0.195,0.195,0.195,1.0)
/// IF SHADOW_COLOR_TEX
		[NoScaleOffset] _STexture ("Shadow Color Texture", 2D) = "white" {}
///
/// IF COLOR_MULTIPLIERS
		_HighlightMultiplier ("Highlight Multiplier", Range(0,4)) = 1
		_ShadowMultiplier ("Shadow Multiplier", Range(0,4)) = 1
///
/// IF COLORMASK
		_MaskedColor ("Masked Color", Color) = (1.0, 0.0, 0.0, 1.0)
		_ColorMaskStrength ("Masked Color Strength", Range(0,4)) = 1.0
///
/// IF WRAP_CUSTOM
		_WrapValue("Diffuse Wrap Value", Range(-0.5,0.5)) = 0.5
///
/// IF DIFFUSE_TINT
		_DiffTint ("Diffuse Tint", Color) = (0.7,0.8,1,1)
///

	[Header(Ramp Shading)]
/// IF TEXTURE_RAMP
		[NoScaleOffset] [TCP2Gradient] _Ramp ("Ramp Texture", 2D) = "gray" {}
/// ELSE
		_RampThreshold("Threshold", Range(0,1)) = 0.5
		_RampSmooth("Main Light Smoothing", Range(0,1)) = 0.2
		_RampSmoothAdd("Other Lights Smoothing", Range(0,1)) = 0.75
///
/// IF TEXTURED_THRESHOLD

	[Header(Threshold Texture)]
	/// IF !(TEXTURED_THRESHOLD_UV0 || TEXTURED_THRESHOLD_UV1)
		[NoScaleOffset]
	///
		_ThresholdTex ("Texture (Alpha)", 2D) = "gray" {}
		_ThresholdStrength ("Strength", Range(0,1)) = 1
	/// IF !(TEXTURED_THRESHOLD_UV0 || TEXTURED_THRESHOLD_UV1)
		_ThresholdScale ("Scale", Float) = 4
	///
///
/// IF SKETCH || SKETCH_GRADIENT

	[Header(Sketch)]
		//SKETCH
		_SketchTex ("Sketch (Alpha)", 2D) = "white" {}
	/// IF SKETCH_ANIM
		_SketchSpeed ("Sketch Anim Speed", Range(1.1, 10)) = 6
	///
	/// IF SKETCH
		_SketchStrength ("Sketch Strength", Range(0,1)) = 1
	/// ELIF SKETCH_GRADIENT
		_SketchColor ("Sketch Color (RGB)", Color) = (0,0,0,1)
		_SketchHalftoneMin ("Sketch Halftone Min", Range(0,1)) = 0.2
		_SketchHalftoneMax ("Sketch Halftone Max", Range(0,1)) = 1.0
	///
///
/// IF HSV_CONTROLS

	[Header(HSV Controls)]
		_HSV_H ("Hue", Range(-360,360)) = 0
		_HSV_S ("Saturation", Range(-1,1)) = 0
		_HSV_V ("Value", Range(-1,1)) = 0
///
/// IF SUBSURFACE_SCATTERING

	[Header(Subsurface Scattering)]
		_SSDistortion ("Distortion", Range(0,2)) = 0.2
		_SSPower ("Power", Range(0.1,16)) = 3.0
		_SSScale ("Scale", Float) = 1.0
	/// IF SUBSURFACE_COLOR
		_SSColor ("Color (RGB)", Color) = (0.5,0.5,0.5,1)
	///
	/// IF SUBSURFACE_AMB_COLOR
		_SSAmbColor ("Ambient Color (RGB)", Color) = (0.5,0.5,0.5,1)
	///
///
/// IF SPECULAR_STYLIZED

	[Header(Stylized Specular)]
		_SpecSmooth("Specular Smoothing", Range(0,1)) = 1.0
		_SpecBlend("Specular Blend", Range(0,1)) = 1.0

///
/// IF FRESNEL_STYLIZED

	[Header(Stylized Fresnel)]
		[PowerSlider(3)] _RimStrength("Strength", Range(0, 2)) = 0.5
		_RimMin("Min", Range(0, 1)) = 0.6
		_RimMax("Max", Range(0, 1)) = 0.85

///
/// IF MASK1 || MASK2 || MASK3

	[Header(Masks)]
///
/// IF MASK1
	/// IF !UVMASK1
		[NoScaleOffset]
	///
		_Mask1 ("Mask 1 (@%MASK1%@)", 2D) = "black" {}
///
/// IF MASK2
	/// IF !UVMASK2
		[NoScaleOffset]
	///
		_Mask2 ("Mask 2 (@%MASK2%@)", 2D) = "black" {}
///
/// IF MASK3
	/// IF !UVMASK3
		[NoScaleOffset]
	///
		_Mask3 ("Mask 3 (@%MASK3%@)", 2D) = "black" {}
///
/// IF PASS_SILHOUETTE

	[Header(Silhouette)]
		_SilhouetteColor ("Color (RGB) Opacity (A)", Color) = (0,0,0,0.5)
	[TCP2Separator]
///
/// IF OUTLINE || OUTLINE_BLENDING

	[Header(Outline)]
		//OUTLINE
	/// IF HDR_OUTLINE
		[HDR] _OutlineColor ("Outline Color", Color) = (0.2, 0.2, 0.2, 1.0)
	/// ELSE
		_OutlineColor ("Outline Color", Color) = (0.2, 0.2, 0.2, 1.0)
	///
		_Outline ("Outline Width", Float) = 1

		//Outline Textured
		[Toggle(TCP2_OUTLINE_TEXTURED)] _EnableTexturedOutline ("Color from Texture", Float) = 0
		[TCP2KeywordFilter(TCP2_OUTLINE_TEXTURED)] _TexLod ("Texture LOD", Range(0,10)) = 5

		//Constant-size outline
		[Toggle(TCP2_OUTLINE_CONST_SIZE)] _EnableConstSizeOutline ("Constant Size Outline", Float) = 0

		//ZSmooth
		[Toggle(TCP2_ZSMOOTH_ON)] _EnableZSmooth ("Correct Z Artefacts", Float) = 0
		//Z Correction & Offset
		[TCP2KeywordFilter(TCP2_ZSMOOTH_ON)] _ZSmooth ("Z Correction", Range(-3.0,3.0)) = -0.5
		[TCP2KeywordFilter(TCP2_ZSMOOTH_ON)] _Offset1 ("Z Offset 1", Float) = 0
		[TCP2KeywordFilter(TCP2_ZSMOOTH_ON)] _Offset2 ("Z Offset 2", Float) = 0

		//This property will be ignored and will draw the custom normals GUI instead
		[TCP2OutlineNormalsGUI] __outline_gui_dummy__ ("_unused_", Float) = 0
	/// IF OUTLINE_BLENDING
		//Blending
		[TCP2Header(OUTLINE BLENDING)]
		[Enum(UnityEngine.Rendering.BlendMode)] _SrcBlendOutline ("Blending Source", Float) = 5
		[Enum(UnityEngine.Rendering.BlendMode)] _DstBlendOutline ("Blending Dest", Float) = 10
	///
	/// IF OUTLINE_BEHIND_STENCIL
			_StencilRef ("Stencil Outline Group", Range(0,255)) = 1
	///
	/// IF OUTLINE_FAKE_RIM
			_OutlineOffset ("Outline Rim Offset", Vector) = (0,0,0,0)
	///
///

/// IF CULL_OPTION
		[Enum(UnityEngine.Rendering.CullMode)] _CullMode ("Culling", Float) = 2
///
		//Avoid compile error if the properties are ending with a drawer
		[HideInInspector] __dummy__ ("__unused__", Float) = 0
	}

	SubShader
	{
/// IF DISABLE_BATCHING
		Tags { "DisableBatching" = "True" }

///
/// IF VERTEXMOTION
		//VertExmotion include file
		CGINCLUDE
		#include "Assets/VertExmotion/Shaders/VertExmotion.cginc"
		ENDCG

///
/// IF PASS_SILHOUETTE
		//Make sure that the objects are rendered later to avoid sorting issues with the transparent silhouette
		Tags { "Queue"="Geometry+10" }
		
		//Silhouette Pass
		Pass
		{
			Blend SrcAlpha OneMinusSrcAlpha
			ZTest Greater
			ZWrite Off			
	/// IF SILHOUETTE_STENCIL

			Stencil
			{
				Ref @%SILHOUETTE_STENCIL_REF%@
				Comp NotEqual
				Pass Replace
				ReadMask @%SILHOUETTE_STENCIL_REF%@
				WriteMask @%SILHOUETTE_STENCIL_REF%@
			}
	///
			
			CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag
				#pragma target 2.0

				#include "UnityCG.cginc"

				fixed4 _SilhouetteColor;

				struct appdata_sil
				{
					float4 vertex : POSITION;
	/// IF VERTEXMOTION
					float4 color : COLOR;
		/// IF VERTEXMOTION_NORMAL
					float3 normal : NORMAL;
					float4 tangent : TANGENT;
		///
	///
					UNITY_VERTEX_INPUT_INSTANCE_ID
				};

				struct v2f_sil
				{
					float4 vertex : SV_POSITION;
					UNITY_VERTEX_OUTPUT_STEREO
				};

				v2f_sil vert (appdata_sil v)
				{
					v2f_sil o;
					UNITY_SETUP_INSTANCE_ID(v);
					UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
	/// IF VERTEXMOTION

					//VertExmotion
		/// IF VERTEXMOTION_SIMPLE
					v.vertex = VertExmotion(v.vertex, v.color);
		/// ELIF VERTEXMOTION_NORMAL
					v.vertex = VertExmotion(v.vertex, v.color, v.normal, v.tangent);
		///

	///
					o.vertex = UnityObjectToClipPos(v.vertex);
					return o;
				}

				fixed4 frag (v2f_sil i) : COLOR
				{
					return _SilhouetteColor;
				}
			ENDCG
		}

///
		Blend [_SrcBlend] [_DstBlend]
		ZWrite [_ZWrite]
/// IF (OUTLINE || OUTLINE_BLENDING) && !LEGACY_OUTLINE

		//================================================================
		// OUTLINE INCLUDE

		CGINCLUDE

		#include "UnityCG.cginc"
/// IF OUTLINE_FAKE_RIM_DIRLIGHT && OFRD_COLOR
		#include "UnityLightingCommon.cginc"
///

		struct a2v
		{
			float4 vertex : POSITION;
			float3 normal : NORMAL;
	#if TCP2_OUTLINE_TEXTURED
			float3 texcoord : TEXCOORD0;
	#endif
	/// IF !OUTLINE_VCOLOR_WIDTH && !VERTEXMOTION
		#if TCP2_COLORS_AS_NORMALS
	///
			float4 color : COLOR;
	/// IF !OUTLINE_VCOLOR_WIDTH && !VERTEXMOTION
		#endif
	///
	#if TCP2_UV2_AS_NORMALS
			float2 uv2 : TEXCOORD1;
	#endif
	/// IF !VERTEXMOTION_NORMAL
	#if TCP2_TANGENT_AS_NORMALS
	///
			float4 tangent : TANGENT;
	/// IF !VERTEXMOTION_NORMAL
	#endif
	///
	#if UNITY_VERSION >= 550
			UNITY_VERTEX_INPUT_INSTANCE_ID
	#endif
		};

		struct v2f
		{
			float4 pos : SV_POSITION;
	#if TCP2_OUTLINE_TEXTURED
			float3 texlod : TEXCOORD1;
	#endif
	/// IF OUTLINE_FAKE_RIM_DIRLIGHT && OFRD_LIGHTING
			float ndl : TEXCOORD2;
	///
			UNITY_VERTEX_OUTPUT_STEREO
		};

		float _Outline;
		float _ZSmooth;
	/// IF HDR_OUTLINE
		half4 _OutlineColor;
	/// ELSE
		fixed4 _OutlineColor;
	///
	/// IF OUTLINE_FAKE_RIM
		half4 _OutlineOffset;
	///

	#if TCP2_OUTLINE_TEXTURED
		sampler2D _MainTex;
		float4 _MainTex_ST;
		float _TexLod;
	#endif

	/// IF OUTLINE_FAKE_RIM_DIRLIGHT
		#define OUTLINE_WIDTH 0.0
	/// ELSE
	  /// IF OUTLINE_VCOLOR_WIDTH
		#define OUTLINE_WIDTH (_Outline * v.color.@%OVCW_CHNL%@)
	  /// ELSE
		#define OUTLINE_WIDTH _Outline
	  ///
	///

		v2f TCP2_Outline_Vert(a2v v)
		{
			v2f o;
			UNITY_SETUP_INSTANCE_ID(v);
			UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

	/// IF VERTEXMOTION

			//VertExmotion
		/// IF VERTEXMOTION_SIMPLE
			v.vertex = VertExmotion(v.vertex, v.color);
		/// ELIF VERTEXMOTION_NORMAL
			v.vertex = VertExmotion(v.vertex, v.color, v.normal, v.tangent);
		///
	///

	/// IF OUTLINE_FAKE_RIM_DIRLIGHT

			float3 objSpaceLight = mul(unity_WorldToObject, _WorldSpaceLightPos0).xyz;
	#ifdef TCP2_OUTLINE_CONST_SIZE
			//Camera-independent outline size
			float dist = distance(_WorldSpaceCameraPos, mul(unity_ObjectToWorld, v.vertex));
			v.vertex.xyz += objSpaceLight.xyz * 0.01 * _Outline * dist;
	#else
			v.vertex.xyz += objSpaceLight.xyz * 0.01 * _Outline;
	#endif
		/// IF OUTLINE_FAKE_RIM_DIRLIGHT && OFRD_LIGHTING
			o.ndl = saturate(dot(v.normal.xyz, objSpaceLight.xyz) * 0.5 + 0.5);
		///
	/// ELIF OUTLINE_FAKE_RIM
			v.vertex += _OutlineOffset;
	///

	#if TCP2_ZSMOOTH_ON
			float4 pos = float4(UnityObjectToViewPos(v.vertex), 1.0);
	#endif

	#ifdef TCP2_COLORS_AS_NORMALS
			//Vertex Color for Normals
			float3 normal = (v.color.xyz*2) - 1;
	#elif TCP2_TANGENT_AS_NORMALS
			//Tangent for Normals
			float3 normal = v.tangent.xyz;
	#elif TCP2_UV2_AS_NORMALS
			//UV2 for Normals
			float3 n;
			//unpack uv2
			v.uv2.x = v.uv2.x * 255.0/16.0;
			n.x = floor(v.uv2.x) / 15.0;
			n.y = frac(v.uv2.x) * 16.0 / 15.0;
			//get z
			n.z = v.uv2.y;
			//transform
			n = n*2 - 1;
			float3 normal = n;
	#else
			float3 normal = v.normal;
	#endif

	#if TCP2_ZSMOOTH_ON
			//Correct Z artefacts
			normal = UnityObjectToViewPos(normal);
			normal.z = -_ZSmooth;
	#endif

/// IF !OUTLINE_FAKE_RIM_DIRLIGHT
	#ifdef TCP2_OUTLINE_CONST_SIZE
			//Camera-independent outline size
			float dist = distance(_WorldSpaceCameraPos, mul(unity_ObjectToWorld, v.vertex));
			#define SIZE	dist
	#else
			#define SIZE	1.0
	#endif
/// ELSE
			#define SIZE	0.0
///

	#if TCP2_ZSMOOTH_ON
			o.pos = mul(UNITY_MATRIX_P, pos + float4(normalize(normal),0) * OUTLINE_WIDTH * 0.01 * SIZE);
	#else
			o.pos = UnityObjectToClipPos(v.vertex + float4(normal,0) * OUTLINE_WIDTH * 0.01 * SIZE);
	#endif

	#if TCP2_OUTLINE_TEXTURED
			half2 uv = TRANSFORM_TEX(v.texcoord, _MainTex);
			o.texlod = tex2Dlod(_MainTex, float4(uv, 0, _TexLod)).rgb;
	#endif

			return o;
		}

	/// IF OUTLINE_FAKE_RIM_DIRLIGHT && OFRD_COLOR && OFRD_LIGHTING
		#define OUTLINE_COLOR (_OutlineColor * _LightColor0 * IN.ndl)
	/// ELIF OUTLINE_FAKE_RIM_DIRLIGHT && OFRD_COLOR
		#define OUTLINE_COLOR (_OutlineColor * _LightColor0)
	/// ELIF OUTLINE_FAKE_RIM_DIRLIGHT && OFRD_LIGHTING
		#define OUTLINE_COLOR (_OutlineColor * IN.ndl)
	/// ELSE
		#define OUTLINE_COLOR _OutlineColor
	///

		float4 TCP2_Outline_Frag (v2f IN) : SV_Target
		{
	#if TCP2_OUTLINE_TEXTURED
			return float4(IN.texlod, 1) * OUTLINE_COLOR;
	#else
			return OUTLINE_COLOR;
	#endif
		}

		ENDCG

		// OUTLINE INCLUDE END
		//================================================================

///
/// IF (OUTLINE || OUTLINE_BLENDING) && OUTLINE_BEHIND_DEPTH
  /// IF LEGACY_OUTLINE
		//Outline
	/// IF OUTLINE
		Tags { "Queue"="Transparent" }
		/// IF FORCE_SM2
		UsePass "Hidden/Toony Colors Pro 2/Outline Only Behind (Shader Model 2)/OUTLINE"
		/// ELSE
		UsePass "Hidden/Toony Colors Pro 2/Outline Only Behind/OUTLINE"
		///
	///
	/// IF OUTLINE_BLENDING
		Tags { "Queue"="Transparent" "RenderType"="Transparent" "IgnoreProjectors"="True" }
		/// IF FORCE_SM2
		UsePass "Hidden/Toony Colors Pro 2/Outline Only Behind (Shader Model 2)/OUTLINE_BLENDING"
		/// ELSE
		UsePass "Hidden/Toony Colors Pro 2/Outline Only Behind/OUTLINE_BLENDING"
		///
	///
  /// ELSE
		//Outline
		Pass
		{
			Cull Off
			ZWrite Off
			Offset [_Offset1],[_Offset2]

	/// IF OUTLINE_BLENDING
			Tags { "LightMode"="ForwardBase" "Queue"="Transparent" "IgnoreProjectors"="True" "RenderType"="Transparent" }
			Blend [_SrcBlendOutline] [_DstBlendOutline]
	/// ELSE
			Tags { "LightMode"="ForwardBase" "Queue"="Transparent" "IgnoreProjectors"="True" }
	///

			CGPROGRAM

			#pragma vertex TCP2_Outline_Vert
			#pragma fragment TCP2_Outline_Frag

			#pragma multi_compile TCP2_NONE TCP2_ZSMOOTH_ON
			#pragma multi_compile TCP2_NONE TCP2_OUTLINE_CONST_SIZE
			#pragma multi_compile TCP2_NONE TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV2_AS_NORMALS
	/// IF !FORCE_SM2
			#pragma multi_compile TCP2_NONE TCP2_OUTLINE_TEXTURED
	///
			#pragma multi_compile_instancing

			#pragma multi_compile EXCLUDE_TCP2_MAIN_PASS

			#pragma target @%SHADER_TARGET%@

			ENDCG
		}
  ///
///
/// IF (OUTLINE || OUTLINE_BLENDING) && OUTLINE_BEHIND_STENCIL

		Stencil
		{
			Ref [_StencilRef]
			Comp Always
			Pass Replace
		}
///
/// IF OUTLINE_BLENDING
		Tags { "Queue"="Transparent" "RenderType"="Transparent" "IgnoreProjectors"="True" "PerformanceChecks"="False" }
/// ELSE
		Tags { "RenderType"="Opaque" "PerformanceChecks"="False" }
///
/// IF CULL_OFF
		Cull Off
/// ELIF CULL_FRONT
		Cull Front
/// ELIF CULL_OPTION
		Cull [_CullMode]
///

		CGPROGRAM

/// IF TESSELLATION
		#include "Tessellation.cginc"
///
		#pragma surface surf StandardTCP2 @%SURF_PARAMS%@ keepalpha exclude_path:deferred exclude_path:prepass
		#pragma target @%SHADER_TARGET%@

		#pragma shader_feature _ _ALPHATEST_ON _ALPHABLEND_ON _ALPHAPREMULTIPLY_ON
	/// IF EMISSION
		#pragma shader_feature _EMISSION
	///

		//================================================================================================================================
		// STRUCTS

		struct Input
		{
			float2 uv_MainTex;
/// IF TEXTURE_BLENDING
	/// IF TEXBLEND_UNITY_SPLATMAP
			half2 uv_Control;
	/// ELIF TEXBLEND_MAP
			half2 uv_TexBlendMap;
	///
///
/// IF UVMASK1
			half2 uv_Mask1;
/// ELIF UVMASK1_UV2
			half2 uv2_Mask1;
///
/// IF UVMASK2
			half2 uv_Mask2;
/// ELIF UVMASK2_UV2
			half2 uv2_Mask2;
///
/// IF UVMASK3
			half2 uv_Mask3;
/// ELIF UVMASK3_UV2
			half2 uv2_Mask3;
///
/// IF TEXTURED_THRESHOLD
	/// IF TEXTURED_THRESHOLD_UV0
			float2 uv_ThresholdTex;
			#define uv_TexturedThreshold uv_ThresholdTex
	/// ELIF TEXTURED_THRESHOLD_UV1
			float2 uv2_ThresholdTex;
			#define uv_TexturedThreshold uv2_ThresholdTex
	/// ELSE
			#define uv_TexturedThreshold uv_MainTex
	///
///
/// IF DETAIL_TEX
	/// IF DETAIL_UV1
			float2 uv2_DetailAlbedoMap;
			#define uv_Detail uv2_DetailAlbedoMap
	/// ELSE
			float2 uv_DetailAlbedoMap;
			#define uv_Detail uv_DetailAlbedoMap
	///
///
/// IF USE_VERTEX_COLORS
			fixed4 vcolor : COLOR;
///
/// IF USE_VIEWDIR
			float3 viewDir;
///
/// IF USE_SCREENSPACE_COORDS_FRAGMENT
			float4 screenCoordsCustom;
///
/// IF (SKETCH || SKETCH_GRADIENT) && SKETCH_VERTEX
			half2 sketchUV;
///
/// IF USE_VFACE
			fixed vface : VFACE;
///
		};

/// IF VERTEX_FUNC || TESSELLATION
		//================================================================================================================================
		// VERTEX FUNCTION

		//Vertex input
		struct appdata_tcp2
		{
			float4 vertex : POSITION;
			float3 normal : NORMAL;
			float4 texcoord : TEXCOORD0;
			float4 texcoord1 : TEXCOORD1;
			float4 texcoord2 : TEXCOORD2;
#	/// IF BUMP || PARALLAX || SPECULAR_ANISOTROPIC || (SNOW_ACCU && SNOW_ACCU_DISP && SNOW_ACCU_NRM_TANGENTS) || VERTEXMOTION_NORMAL
			float4 tangent : TANGENT;
#	/// ELSE IF !NO_LIGHTMAP
		#if defined(LIGHTMAP_ON) && defined(DIRLIGHTMAP_COMBINED)
			float4 tangent : TANGENT;
		#endif
#	///
	/// IF USE_VERTEX_COLORS || (SNOW_ACCU && SNOW_ACCU_DISP && SNOW_ACCU_NRM_COLOR)
			fixed4 color : COLOR;
	///
			UNITY_VERTEX_INPUT_INSTANCE_ID
		};
	/// IF TESSELLATION_PHONG

		//Phong Tessellation
		float _Phong;
		float _EdgeLength;
		void dispNone(inout appdata_tcp2 v) { }
		float4 tessEdge(appdata_tcp2 v0, appdata_tcp2 v1, appdata_tcp2 v2)
		{
			return UnityEdgeLengthBasedTess (v0.vertex, v1.vertex, v2.vertex, _EdgeLength);
		}
	/// ELIF TESSELLATION_FIXED || TESSELLATION_DIST || TESSELLATION_EDGE_LENGTH

		//Tessellation
		float _Tess;
		float _Displacement;
		/// IF !(BUMP && PARALLAX) && TESSELLATION_HEIGHT_MAP
		sampler2D _ParallaxMap;
		///
		/// IF TESSELLATION_DIST
		float _TessDistMin, _TessDistMax;
		///
		/// IF TESSELLATION_EDGE_LENGTH
		float _EdgeLength;
		///

		/// IF TESSELLATION_FIXED
		float4 tessFixed() { return _Tess; }
		/// ELIF TESSELLATION_DIST
		float4 tessDistance(appdata_tcp2 v0, appdata_tcp2 v1, appdata_tcp2 v2) { return UnityDistanceBasedTess(v0.vertex, v1.vertex, v2.vertex, _TessDistMin, _TessDistMax, _Tess); }
		/// ELIF TESSELLATION_EDGE_LENGTH
		float4 tessEdge(appdata_tcp2 v0, appdata_tcp2 v1, appdata_tcp2 v2) { return UnityEdgeLengthBasedTess (v0.vertex, v1.vertex, v2.vertex, _EdgeLength); }
		///

		void disp(inout appdata_tcp2 v)
		{
		/// IF TESSELLATION_HEIGHT_MAP
			float disp = tex2Dlod(_ParallaxMap, float4(v.texcoord.xy,0,0)).r * _Displacement;
		/// ELSE
			float disp = tex2Dlod(_MainTex, float4(v.texcoord.xy,0,0)).a * _Displacement;
		///
			v.vertex.xyz += v.normal.xyz * disp;
		}
	///
	
		//Vertex function
		void vert (inout appdata_tcp2 v, out Input o)
		{
			UNITY_INITIALIZE_OUTPUT(Input, o);
	/// IF VERTEXMOTION

			//VertExmotion
		/// IF VERTEXMOTION_SIMPLE
			v.vertex = VertExmotion(v.vertex, v.color);
		/// ELIF VERTEXMOTION_NORMAL
			v.vertex = VertExmotion(v.vertex, v.color, v.normal, v.tangent);
		///
	///
	/// IF USE_SCREENSPACE_COORDS_VERTEX
			
			float4 pos = mul(UNITY_MATRIX_MVP, v.vertex);
			float4 screenCoords = ComputeScreenPos(pos);
		/// IF USE_SCREENSPACE_COORDS_FRAGMENT
			o.screenCoordsCustom = screenCoords;
		///
		/// IF SKETCH || SKETCH_GRADIENT
			
			//Sketch
			/// IF SKETCH_VERTEX
			float2 screenUV = screenCoords.xy / screenCoords.w;
			float screenRatio = _ScreenParams.y / _ScreenParams.x;
			screenUV.y *= screenRatio;
				/// IF !NO_SKETCH_OFFSET
			ObjSpaceUVOffset(screenUV, screenRatio);
				///
			o.sketchUV.xy = screenUV;
			o.sketchUV.xy = TRANSFORM_TEX(o.sketchUV.xy, _SketchTex);
			///
			/// IF SKETCH_VERTEX && SKETCH_ANIM
			float2 random = round(float2(_Time.z, -_Time.z) * _SketchSpeed) / _SketchSpeed;
			o.sketchUV.xy += frac(random.xy);
			///
		///
	///
		}

///
		//================================================================================================================================
		// LIGHTING FUNCTION

		inline half4 LightingStandardTCP2(SurfaceOutputStandardTCP2 s, half3 viewDir, UnityGI gi)
		{
			s.Normal = normalize(s.Normal);
/// IF USE_VFACE
			s.Normal.xyz *= s.vface;
///

/// IF PBS_SPECULAR
			// energy conservation
			half oneMinusReflectivity;
			s.Albedo = EnergyConservationBetweenDiffuseAndSpecular (s.Albedo, s.Specular, /*out*/ oneMinusReflectivity);
/// ELSE
			half oneMinusReflectivity;
			half3 specColor;
			s.Albedo = DiffuseAndSpecularFromMetallic(s.Albedo, s.Metallic, /*out*/ specColor, /*out*/ oneMinusReflectivity);
///

			// shader relies on pre-multiply alpha-blend (_SrcBlend = One, _DstBlend = OneMinusSrcAlpha)
			// this is necessary to handle transparency in physically correct way - only diffuse component gets affected by alpha
			half outputAlpha;
			s.Albedo = PreMultiplyAlpha(s.Albedo, s.Alpha, oneMinusReflectivity, /*out*/ outputAlpha);

		#if defined(UNITY_PASS_FORWARDBASE)
			fixed atten = s.atten;
		#else
			fixed atten = 1;
		#endif

/// IF PBS_SPECULAR
			half4 c = TCP2_BRDF_PBS(s.Albedo, s.Specular, oneMinusReflectivity, s.Smoothness, s.Normal, viewDir, gi.light, gi.indirect, /* TCP2 */ atten, s
/// ELSE
			half4 c = TCP2_BRDF_PBS(s.Albedo, specColor, oneMinusReflectivity, s.Smoothness, s.Normal, viewDir, gi.light, gi.indirect, /* TCP2 */ atten, s
///

/// IF TEXTURED_THRESHOLD
				,s.texThresholdTexcoords
///
/// IF (SKETCH || SKETCH_GRADIENT) && !SKETCH_VERTEX
				,s.screenCoords
/// ELIF (SKETCH || SKETCH_GRADIENT) && SKETCH_VERTEX
				,s.sketchUV
///
				);
			c.a = outputAlpha;
			return c;
		}

		inline void LightingStandardTCP2_GI(inout SurfaceOutputStandardTCP2 s, UnityGIInput data, inout UnityGI gi)
		{
/// IF PBS_SPECULAR
			Unity_GlossyEnvironmentData g = UnityGlossyEnvironmentSetup(s.Smoothness, data.worldViewDir, s.Normal, s.Specular);
/// ELSE
			Unity_GlossyEnvironmentData g = UnityGlossyEnvironmentSetup(s.Smoothness, data.worldViewDir, s.Normal, lerp(unity_ColorSpaceDielectricSpec.rgb, s.Albedo, s.Metallic));
///
			gi = UnityGlobalIllumination(data, s.Occlusion, s.Normal, g);

			s.atten = data.atten;				//transfer attenuation to lighting function
			gi.light.color = _LightColor0.rgb;	//remove attenuation
		}

		//================================================================================================================================
		// SURFACE FUNCTION

/// IF TEXTURE_BLENDING && TEXBLEND_HEIGHT

		// Height-based texture blending
		float4 blend_height_smooth(float4 texture1, float height1, float4 texture2, float height2, float smoothing)
		{
			float ma = max(texture1.a + height1, texture2.a + height2) - smoothing;
			float b1 = max(texture1.a + height1 - ma, 0);
			float b2 = max(texture2.a + height2 - ma, 0);
			return (texture1 * b1 + texture2 * b2) / (b1 + b2);
		}
///
/// IF NOTILE_TEXTURES

	// No Tiling texture fetch function
	// (c) 2017 Inigo Quilez - MIT License
	// Source: http://www.iquilezles.org/www/articles/texturerepetition/texturerepetition.htm
	float4 tex2DnoTile( sampler2D samp, in float2 uv )
	{
		// sample variation pattern    
		float k = tex2D(_NoTileNoiseTex, (1/_NoTileNoiseTex_TexelSize.zw)*uv).a; // cheap (cache friendly) lookup    

		// compute index    
		float index = k*8.0;
		float i = floor(index);
		float f = frac(index);

		// offsets for the different virtual patterns    
		float2 offa = sin(float2(3.0,7.0)*(i+0.0)); // can replace with any other hash    
		float2 offb = sin(float2(3.0,7.0)*(i+1.0)); // can replace with any other hash    

		// compute derivatives for mip-mapping    
		float2 dx = ddx(uv);
		float2 dy = ddy(uv);

		// sample the two closest virtual patterns    
		float4 cola = tex2Dgrad(samp, uv + offa, dx, dy);
		float4 colb = tex2Dgrad(samp, uv + offb, dx, dy);

		// interpolate between the two virtual patterns    
		return lerp(cola, colb, smoothstep(0.2,0.8,f-0.1*dot(cola-colb, 1)));
	}
///
		void surf (Input IN, inout SurfaceOutputStandardTCP2 o)
		{
/// IF BUMP && PARALLAX
			//Parallax Offset
			fixed height = tex2D(_ParallaxMap, IN.uv_MainTex).a;
			float2 offset = ParallaxOffset(height, _Parallax, IN.viewDir);
			IN.uv_MainTex += offset;
///
/// IF MASK1 || MASK2 || MASK3

			//Masks
///
/// IF MASK1
	/// IF UVMASK1
			fixed4 mask1 = tex2D(_Mask1, IN.uv_Mask1);
	/// ELIF UVMASK1_UV2
			fixed4 mask1 = tex2D(_Mask1, IN.uv2_Mask1);
	/// ELSE
			fixed4 mask1 = tex2D(_Mask1, IN.uv_MainTex);
	///
///
/// IF MASK2
	/// IF UVMASK2
			fixed4 mask2 = tex2D(_Mask2, IN.uv_Mask2);
	/// ELIF UVMASK2_UV2
			fixed4 mask2 = tex2D(_Mask2, IN.uv2_Mask2);
	/// ELSE
			fixed4 mask2 = tex2D(_Mask2, IN.uv_MainTex);
	///
///
/// IF MASK3
	/// IF UVMASK3
			fixed4 mask3 = tex2D(_Mask3, IN.uv_Mask3);
	/// ELIF UVMASK3_UV2
			fixed4 mask3 = tex2D(_Mask3, IN.uv2_Mask3);
	/// ELSE
			fixed4 mask3 = tex2D(_Mask3, IN.uv_MainTex);
	///
///

			fixed4 mainTex = tex2D (_MainTex, IN.uv_MainTex) * _Color;
/// IF TEXTURE_BLENDING

			//Texture Blending
	/// IF TEXBLEND_VCOLORS
			#define BLEND_SOURCE IN.vcolor
	/// ELIF TEXBLEND_MAP
			float4 texblend_map = tex2D(_TexBlendMap, IN.uv_TexBlendMap);
			#define BLEND_SOURCE texblend_map
	/// ELIF TEXBLEND_UNITY_SPLATMAP
			float4 splat_control = tex2D(_Control, IN.uv_Control).gbar;
			#define BLEND_SOURCE splat_control
	///
	/// IF TEXBLEND_NORMALIZE
			BLEND_SOURCE.rgb = saturate(normalize(BLEND_SOURCE.rgb) * dot(_BlendContrast.xxx, BLEND_SOURCE.rgb));
	///

	/// IF BLEND_TEX1
		/// IF BLEND_TEX1_NOTILE
			fixed4 tex1 = tex2DnoTile(_BlendTex1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
		/// ELSE
			fixed4 tex1 = tex2D(_BlendTex1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
		///
	///
	/// IF BLEND_TEX2
		/// IF BLEND_TEX2_NOTILE
			fixed4 tex2 = tex2DnoTile(_BlendTex2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
		/// ELSE
			fixed4 tex2 = tex2D(_BlendTex2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
		///
	///
	/// IF BLEND_TEX3
		/// IF BLEND_TEX3_NOTILE
			fixed4 tex3 = tex2DnoTile(_BlendTex3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
		/// ELSE
			fixed4 tex3 = tex2D(_BlendTex3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
		///
	///
	/// IF BLEND_TEX4
		/// IF BLEND_TEX4_NOTILE
			fixed4 tex4 = tex2DnoTile(_BlendTex4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
		/// ELSE
			fixed4 tex4 = tex2D(_BlendTex4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
		///
	///

	/// IF TEXBLEND_LINEAR
		/// IF BLEND_TEX1
			mainTex = lerp(mainTex, tex1, BLEND_SOURCE.@%BLEND_TEX1_CHNL%@);
		///
		/// IF BLEND_TEX2
			mainTex = lerp(mainTex, tex2, BLEND_SOURCE.@%BLEND_TEX2_CHNL%@);
		///
		/// IF BLEND_TEX3
			mainTex = lerp(mainTex, tex3, BLEND_SOURCE.@%BLEND_TEX3_CHNL%@);
		///
		/// IF BLEND_TEX4
			mainTex = lerp(mainTex, tex4, BLEND_SOURCE.@%BLEND_TEX4_CHNL%@);
		///
	/// ELIF TEXBLEND_LINEAR_ADD
			float blackChannel = 1 - dot(BLEND_SOURCE.rgba, half4(1, 1, 1, 1));
			mainTex *= blackChannel;

		/// IF BLEND_TEX1
			mainTex += tex1 * BLEND_SOURCE.@%BLEND_TEX1_CHNL%@;
		///
		/// IF BLEND_TEX2
			mainTex += tex2 * BLEND_SOURCE.@%BLEND_TEX2_CHNL%@;
		///
		/// IF BLEND_TEX3
			mainTex += tex3 * BLEND_SOURCE.@%BLEND_TEX3_CHNL%@;
		///
		/// IF BLEND_TEX4
			mainTex += tex4 * BLEND_SOURCE.@%BLEND_TEX4_CHNL%@;
		///
	/// ELIF TEXBLEND_HEIGHT

			#define CONTRAST @%TEXBLEND_HEIGHT_CONTRAST%@
			#define CONTRAST_half CONTRAST/2

		/// IF BLEND_TEX1
			mainTex = lerp(mainTex, blend_height_smooth(mainTex, mainTex.a, tex1, BLEND_SOURCE.r * CONTRAST - CONTRAST_half + tex1.a + _VColorBlendOffset.x, _VColorBlendSmooth.x), saturate(BLEND_SOURCE.r * CONTRAST_half));
		///
		/// IF BLEND_TEX2
			mainTex = lerp(mainTex, blend_height_smooth(mainTex, mainTex.a, tex2, BLEND_SOURCE.g * CONTRAST - CONTRAST_half + tex2.a + _VColorBlendOffset.y, _VColorBlendSmooth.y), saturate(BLEND_SOURCE.g * CONTRAST_half));
		///
		/// IF BLEND_TEX3
			mainTex = lerp(mainTex, blend_height_smooth(mainTex, mainTex.a, tex3, BLEND_SOURCE.b * CONTRAST - CONTRAST_half + tex3.a + _VColorBlendOffset.z, _VColorBlendSmooth.z), saturate(BLEND_SOURCE.b * CONTRAST_half));
		///
		/// IF BLEND_TEX4
			mainTex = lerp(mainTex, blend_height_smooth(mainTex, mainTex.a, tex4, BLEND_SOURCE.a * CONTRAST - CONTRAST_half + tex4.a + _VColorBlendOffset.w, _VColorBlendSmooth.w), saturate(BLEND_SOURCE.a * CONTRAST_half));
		///
	///
///
			o.Albedo = mainTex.rgb;
			o.Alpha = mainTex.a;

		#if _ALPHATEST_ON
			clip(o.Alpha - _Cutoff);
		#endif

/// IF DETAIL_TEX
			
			//Detail texture
	/// IF DETAIL_MASK
			half detailMask = tex2D(_DetailMask, IN.uv_MainTex.xy).a;
	/// ELSE
			half detailMask = 1;
	///
			half3 detailAlbedo = tex2D (_DetailAlbedoMap, IN.uv_Detail.xy).rgb;
	/// IF DETAIL_BLEND_MULX2
			o.Albedo *= LerpWhiteTo (detailAlbedo * unity_ColorSpaceDouble.rgb, detailMask);
	/// ELIF DETAIL_BLEND_MUL
			o.Albedo *= LerpWhiteTo (detailAlbedo, detailMask);
	/// ELIF DETAIL_BLEND_ADD
			o.Albedo += detailAlbedo * detailMask;
	/// ELIF DETAIL_BLEND_LERP
			o.Albedo = lerp (o.Albedo, detailAlbedo, detailMask);
	///
///
/// IF PBS_SPECULAR
			//Specular workflow
  /// IF SPEC_METALLIC_GLOSS_MAP
			fixed4 specGlossMap = tex2D(_SpecGlossMap, IN.uv_MainTex);
	/// IF TEXTURE_BLENDING && TEXBLEND_SPEC_METALLIC
		/// IF BLEND_TEX1
			/// IF BLEND_TEX1_NOTILE
			fixed4 spec1 = tex2DnoTile(_SpecGlossMap1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
			/// ELSE
			fixed4 spec1 = tex2D(_SpecGlossMap1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
			///
		///
		/// IF BLEND_TEX2
			/// IF BLEND_TEX2_NOTILE
			fixed4 spec2 = tex2DnoTile(_SpecGlossMap2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
			/// ELSE
			fixed4 spec2 = tex2D(_SpecGlossMap2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
			///
		///
		/// IF BLEND_TEX3
			/// IF BLEND_TEX3_NOTILE
			fixed4 spec3 = tex2DnoTile(_SpecGlossMap3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
			/// ELSE
			fixed4 spec3 = tex2D(_SpecGlossMap3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
			///
		///
		/// IF BLEND_TEX4
			/// IF BLEND_TEX4_NOTILE
			fixed4 spec4 = tex2DnoTile(_SpecGlossMap4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
			/// ELSE
			fixed4 spec4 = tex2D(_SpecGlossMap4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
			///
		///

		/// IF TEXBLEND_LINEAR
			/// IF BLEND_TEX1
			specGlossMap = lerp(specGlossMap, spec1, BLEND_SOURCE.@%BLEND_TEX1_CHNL%@);
			///
			/// IF BLEND_TEX2
			specGlossMap = lerp(specGlossMap, spec2, BLEND_SOURCE.@%BLEND_TEX2_CHNL%@);
			///
			/// IF BLEND_TEX3
			specGlossMap = lerp(specGlossMap, spec3, BLEND_SOURCE.@%BLEND_TEX3_CHNL%@);
			///
			/// IF BLEND_TEX4
			specGlossMap = lerp(specGlossMap, spec4, BLEND_SOURCE.@%BLEND_TEX4_CHNL%@);
			///
		/// ELIF TEXBLEND_LINEAR_ADD
			float blackChannel = 1 - dot(BLEND_SOURCE.rgba, half4(1, 1, 1, 1));
			specGlossMap *= blackChannel;

			/// IF BLEND_TEX1
			specGlossMap += spec1 * BLEND_SOURCE.@%BLEND_TEX1_CHNL%@;
			///
			/// IF BLEND_TEX2
			specGlossMap += spec2 * BLEND_SOURCE.@%BLEND_TEX2_CHNL%@;
			///
			/// IF BLEND_TEX3
			specGlossMap += spec3 * BLEND_SOURCE.@%BLEND_TEX3_CHNL%@;
			///
			/// IF BLEND_TEX4
			specGlossMap += spec4 * BLEND_SOURCE.@%BLEND_TEX4_CHNL%@;
			///
		/// ELIF TEXBLEND_HEIGHT

			#define CONTRAST @%TEXBLEND_HEIGHT_CONTRAST%@
			#define CONTRAST_half CONTRAST/2

			/// IF BLEND_TEX1
			specGlossMap = lerp(specGlossMap, blend_height_smooth(specGlossMap, specGlossMap.a, spec1, BLEND_SOURCE.r * CONTRAST - CONTRAST_half + tex1.a + _VColorBlendOffset.x, _VColorBlendSmooth.x), saturate(BLEND_SOURCE.r * CONTRAST_half));
			///
			/// IF BLEND_TEX2
			specGlossMap = lerp(specGlossMap, blend_height_smooth(specGlossMap, specGlossMap.a, spec2, BLEND_SOURCE.g * CONTRAST - CONTRAST_half + tex2.a + _VColorBlendOffset.y, _VColorBlendSmooth.y), saturate(BLEND_SOURCE.g * CONTRAST_half));
			///
			/// IF BLEND_TEX3
			specGlossMap = lerp(specGlossMap, blend_height_smooth(specGlossMap, specGlossMap.a, spec3, BLEND_SOURCE.b * CONTRAST - CONTRAST_half + tex3.a + _VColorBlendOffset.z, _VColorBlendSmooth.z), saturate(BLEND_SOURCE.b * CONTRAST_half));
			///
			/// IF BLEND_TEX4
			specGlossMap = lerp(specGlossMap, blend_height_smooth(specGlossMap, specGlossMap.a, spec4, BLEND_SOURCE.a * CONTRAST - CONTRAST_half + tex4.a + _VColorBlendOffset.w, _VColorBlendSmooth.w), saturate(BLEND_SOURCE.a * CONTRAST_half));
			///
		///
	///
  /// ELSE
			fixed4 specGlossMap = fixed4(0,0,0,0);
  ///
			half4 specGloss = SpecularGloss(mainTex.a, specGlossMap);
			half3 specColor = specGloss.rgb;
			half smoothness = specGloss.a;
			o.Specular = specColor;
/// ELSE

			//Metallic Workflow
  /// IF SPEC_METALLIC_GLOSS_MAP
			fixed4 metalGlossMap = tex2D(_MetallicGlossMap, IN.uv_MainTex);
	/// IF TEXTURE_BLENDING && TEXBLEND_SPEC_METALLIC
		/// IF BLEND_TEX1
			/// IF BLEND_TEX1_NOTILE
			fixed4 metal1 = tex2DnoTile(_MetallicGlossMap1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
			/// ELSE
			fixed4 metal1 = tex2D(_MetallicGlossMap1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
			///
		///
		/// IF BLEND_TEX2
			/// IF BLEND_TEX2_NOTILE
			fixed4 metal2 = tex2DnoTile(_MetallicGlossMap2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
			/// ELSE
			fixed4 metal2 = tex2D(_MetallicGlossMap2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
			///
		///
		/// IF BLEND_TEX3
			/// IF BLEND_TEX3_NOTILE
			fixed4 metal3 = tex2DnoTile(_MetallicGlossMap3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
			/// ELSE
			fixed4 metal3 = tex2D(_MetallicGlossMap3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
			///
		///
		/// IF BLEND_TEX4
			/// IF BLEND_TEX4_NOTILE
			fixed4 metal4 = tex2DnoTile(_MetallicGlossMap4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
			/// ELSE
			fixed4 metal4 = tex2D(_MetallicGlossMap4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
			///
		///

		/// IF TEXBLEND_LINEAR
			/// IF BLEND_TEX1
			metalGlossMap = lerp(metalGlossMap, metal1, BLEND_SOURCE.@%BLEND_TEX1_CHNL%@);
			///
			/// IF BLEND_TEX2
			metalGlossMap = lerp(metalGlossMap, metal2, BLEND_SOURCE.@%BLEND_TEX2_CHNL%@);
			///
			/// IF BLEND_TEX3
			metalGlossMap = lerp(metalGlossMap, metal3, BLEND_SOURCE.@%BLEND_TEX3_CHNL%@);
			///
			/// IF BLEND_TEX4
			metalGlossMap = lerp(metalGlossMap, metal4, BLEND_SOURCE.@%BLEND_TEX4_CHNL%@);
			///
		/// ELIF TEXBLEND_LINEAR_ADD
			float blackChannel = 1 - dot(BLEND_SOURCE.rgba, half4(1, 1, 1, 1));
			metalGlossMap *= blackChannel;

			/// IF BLEND_TEX1
			metalGlossMap += metal1 * BLEND_SOURCE.@%BLEND_TEX1_CHNL%@;
			///
			/// IF BLEND_TEX2
			metalGlossMap += metal2 * BLEND_SOURCE.@%BLEND_TEX2_CHNL%@;
			///
			/// IF BLEND_TEX3
			metalGlossMap += metal3 * BLEND_SOURCE.@%BLEND_TEX3_CHNL%@;
			///
			/// IF BLEND_TEX4
			metalGlossMap += metal4 * BLEND_SOURCE.@%BLEND_TEX4_CHNL%@;
			///
		/// ELIF TEXBLEND_HEIGHT

			#define CONTRAST @%TEXBLEND_HEIGHT_CONTRAST%@
			#define CONTRAST_half CONTRAST/2

			/// IF BLEND_TEX1
			metalGlossMap = lerp(metalGlossMap, blend_height_smooth(metalGlossMap, metalGlossMap.a, metal1, BLEND_SOURCE.r * CONTRAST - CONTRAST_half + tex1.a + _VColorBlendOffset.x, _VColorBlendSmooth.x), saturate(BLEND_SOURCE.r * CONTRAST_half));
			///
			/// IF BLEND_TEX2
			metalGlossMap = lerp(metalGlossMap, blend_height_smooth(metalGlossMap, metalGlossMap.a, metal2, BLEND_SOURCE.g * CONTRAST - CONTRAST_half + tex2.a + _VColorBlendOffset.y, _VColorBlendSmooth.y), saturate(BLEND_SOURCE.g * CONTRAST_half));
			///
			/// IF BLEND_TEX3
			metalGlossMap = lerp(metalGlossMap, blend_height_smooth(metalGlossMap, metalGlossMap.a, metal3, BLEND_SOURCE.b * CONTRAST - CONTRAST_half + tex3.a + _VColorBlendOffset.z, _VColorBlendSmooth.z), saturate(BLEND_SOURCE.b * CONTRAST_half));
			///
			/// IF BLEND_TEX4
			metalGlossMap = lerp(metalGlossMap, blend_height_smooth(metalGlossMap, metalGlossMap.a, metal4, BLEND_SOURCE.a * CONTRAST - CONTRAST_half + tex4.a + _VColorBlendOffset.w, _VColorBlendSmooth.w), saturate(BLEND_SOURCE.a * CONTRAST_half));
			///
		///
	///
  /// ELSE
			fixed4 metalGlossMap = fixed4(0,0,0,0);
  ///
			half2 metallicGloss = MetallicGloss(mainTex.a, metalGlossMap);
			half metallic = metallicGloss.x;
			half smoothness = metallicGloss.y;
			o.Metallic = metallic;
///
			o.Smoothness = smoothness;

/// IF TEXTURED_THRESHOLD

			o.texThresholdTexcoords = IN.uv_TexturedThreshold;
///
/// IF SHADOW_COLOR_TEX

			//Shadow Color Texture
			fixed4 shadowTex = tex2D(_STexture, IN.uv_MainTex);
			o.ShadowColorTex = shadowTex.rgb;
///
/// IF VCOLORS

			//Vertex Colors
			float4 vertexColors = IN.vcolor;
	/// IF VCOLORS_LINEAR
		#if UNITY_VERSION >= 550
		  #ifndef UNITY_COLORSPACE_GAMMA
			vertexColors.rgb = GammaToLinearSpace(vertexColors.rgb);
		  #endif
		#else
			vertexColors.rgb = IsGammaSpace() ? vertexColors.rgb : GammaToLinearSpace(vertexColors.rgb);
		#endif
	///
			o.Albedo *= vertexColors;
///
/// IF HSV_CONTROLS

			//Albedo HSV
			float3 diffHSV = rgb2hsv(o.Albedo.rgb);
			diffHSV += float3(_HSV_H/360,_HSV_S,_HSV_V);
	/// IF HSV_MASK
			o.Albedo.rgb = lerp(o.Albedo.rgb, hsv2rgb(diffHSV), @%HSV_MASK%@@%HSV_MASK_CHANNEL%@);
	/// ELSE
			o.Albedo.rgb = hsv2rgb(diffHSV);
	///
///
/// IF COLORMASK
	/// IF COLORMASK_ADD
			o.Albedo += lerp(fixed3(0,0,0), _MaskedColor.rgb, @%COLORMASK%@@%COLORMASK_CHANNEL%@ * _ColorMaskStrength);
	/// ELIF COLORMASK_REPLACE
			o.Albedo = lerp(o.Albedo, _MaskedColor.rgb, @%COLORMASK%@@%COLORMASK_CHANNEL%@ * _ColorMaskStrength);
	/// ELSE
			o.Albedo *= lerp(fixed3(1,1,1), _MaskedColor.rgb, @%COLORMASK%@@%COLORMASK_CHANNEL%@ * _ColorMaskStrength);
	///
///
/// IF BUMP

			//Bump/normal map
			half4 normalMap = tex2D(_BumpMap, IN.uv_MainTex.xy);
	/// IF TEXTURE_BLENDING && TEXBLEND_BUMP

			//Texture Blending (Normal maps)
	  /// IF BLEND_TEX1
		/// IF BLEND_TEX1_NOTILE
			fixed4 bump1 = tex2DnoTile(_BumpMap1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
		/// ELSE
			fixed4 bump1 = tex2D(_BumpMap1, IN.uv_MainTex * _BlendTex1_ST.xy + _BlendTex1_ST.zw);
		///
	  ///
	  /// IF BLEND_TEX2
		/// IF BLEND_TEX2_NOTILE
			fixed4 bump2 = tex2DnoTile(_BumpMap2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
		/// ELSE
			fixed4 bump2 = tex2D(_BumpMap2, IN.uv_MainTex * _BlendTex2_ST.xy + _BlendTex2_ST.zw);
		///
	  ///
	  /// IF BLEND_TEX3
		/// IF BLEND_TEX3_NOTILE
			fixed4 bump3 = tex2DnoTile(_BumpMap3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
		/// ELSE
			fixed4 bump3 = tex2D(_BumpMap3, IN.uv_MainTex * _BlendTex3_ST.xy + _BlendTex3_ST.zw);
		///
	  ///
	  /// IF BLEND_TEX4
		/// IF BLEND_TEX4_NOTILE
			fixed4 bump4 = tex2DnoTile(_BumpMap4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
		/// ELSE
			fixed4 bump4 = tex2D(_BumpMap4, IN.uv_MainTex * _BlendTex4_ST.xy + _BlendTex4_ST.zw);
		///
	  ///

	  /// IF TEXBLEND_LINEAR
		/// IF BLEND_TEX1
			normalMap = lerp(normalMap, bump1, BLEND_SOURCE.@%BLEND_TEX1_CHNL%@);
		///
		/// IF BLEND_TEX2
			normalMap = lerp(normalMap, bump2, BLEND_SOURCE.@%BLEND_TEX2_CHNL%@);
		///
		/// IF BLEND_TEX3
			normalMap = lerp(normalMap, bump3, BLEND_SOURCE.@%BLEND_TEX3_CHNL%@);
		///
		/// IF BLEND_TEX4
			normalMap = lerp(normalMap, bump4, BLEND_SOURCE.@%BLEND_TEX4_CHNL%@);
		///
	  /// ELIF TEXBLEND_LINEAR_ADD
			normalMap *= blackChannel;

		/// IF BLEND_TEX1
			normalMap += bump1 * BLEND_SOURCE.@%BLEND_TEX1_CHNL%@;
		///
		/// IF BLEND_TEX2
			normalMap += bump2 * BLEND_SOURCE.@%BLEND_TEX2_CHNL%@;
		///
		/// IF BLEND_TEX3
			normalMap += bump3 * BLEND_SOURCE.@%BLEND_TEX3_CHNL%@;
		///
		/// IF BLEND_TEX4
			normalMap += bump4 * BLEND_SOURCE.@%BLEND_TEX4_CHNL%@;
		///
	  /// ELIF TEXBLEND_HEIGHT

			#define CONTRAST @%TEXBLEND_HEIGHT_CONTRAST%@
			#define CONTRAST_half CONTRAST/2

		/// IF BLEND_TEX1
			normalMap = lerp(normalMap, blend_height_smooth(normalMap, normalMap.a, bump1, BLEND_SOURCE.r * CONTRAST - CONTRAST_half + tex1.a + _VColorBlendOffset.x, _VColorBlendSmooth.x), saturate(BLEND_SOURCE.r * CONTRAST_half));
		///
		/// IF BLEND_TEX2
			normalMap = lerp(normalMap, blend_height_smooth(normalMap, normalMap.a, bump2, BLEND_SOURCE.g * CONTRAST - CONTRAST_half + tex2.a + _VColorBlendOffset.y, _VColorBlendSmooth.y), saturate(BLEND_SOURCE.g * CONTRAST_half));
		///
		/// IF BLEND_TEX3
			normalMap = lerp(normalMap, blend_height_smooth(normalMap, normalMap.a, bump3, BLEND_SOURCE.b * CONTRAST - CONTRAST_half + tex3.a + _VColorBlendOffset.z, _VColorBlendSmooth.z), saturate(BLEND_SOURCE.b * CONTRAST_half));
		///
		/// IF BLEND_TEX4
			normalMap = lerp(normalMap, blend_height_smooth(normalMap, normalMap.a, bump4, BLEND_SOURCE.a * CONTRAST - CONTRAST_half + tex4.a + _VColorBlendOffset.w, _VColorBlendSmooth.w), saturate(BLEND_SOURCE.a * CONTRAST_half));
		///
	  ///
	///
			o.Normal = UnpackScaleNormal(normalMap, _BumpScale);
	/// IF DETAIL_TEX && DETAIL_BUMP
			half3 detailNormalTangent = UnpackScaleNormal(tex2D(_DetailNormalMap, IN.uv_Detail.xy), _DetailNormalMapScale);
		/// IF DETAIL_BLEND_LERP
			o.Normal = lerp(o.Normal, detailNormalTangent, detailMask);
		/// ELSE
			o.Normal = lerp(o.Normal, BlendNormals(o.Normal, detailNormalTangent), detailMask);
		///
	///
///
/// IF EMISSION

			//Emission
		#if _EMISSION
			o.Emission += tex2D(_EmissionMap, IN.uv_MainTex.xy) * _EmissionColor.rgb;
		#endif
///
/// IF OCCLUSION

			//Occlusion
			o.Occlusion = LerpOneTo(tex2D(_OcclusionMap, IN.uv_MainTex.xy).@%OCCLUSION_CHNL%@, _OcclusionStrength);
///
/// IF SUBSURFACE_SCATTERING && SS_MASK
			o.SubsurfaceMask = @%SS_MASK%@@%SS_MASK_CHANNEL%@;
///
/// IF USE_SCREENSPACE_COORDS_FRAGMENT
			o.screenCoords = IN.screenCoordsCustom;
///
/// IF (SKETCH || SKETCH_GRADIENT) && SKETCH_VERTEX
			o.sketchUV = IN.sketchUV;
///
/// IF USE_VFACE
			o.vface = IN.vface;
///
		#ifdef _ALPHABLEND_ON
			o.Albedo *= o.Alpha;
		#endif
		}
		ENDCG

/// IF (OUTLINE || OUTLINE_BLENDING) && !OUTLINE_BEHIND_DEPTH
  /// IF LEGACY_OUTLINE
	/// IF OUTLINE_BEHIND_STENCIL
		//Outline behind stencil
		UsePass "Hidden/Toony Colors Pro 2/Outline Stencil/OUTLINE"
	/// ELIF OUTLINE
		//Outline
		/// IF FORCE_SM2
		UsePass "Hidden/Toony Colors Pro 2/Outline Only (Shader Model 2)/OUTLINE"
		/// ELSE
		UsePass "Hidden/Toony Colors Pro 2/Outline Only/OUTLINE"
		///
	/// ELIF OUTLINE_BLENDING
		//Outline
		Tags { "Queue"="Transparent" "RenderType"="Transparent" "IgnoreProjectors"="True" }
		/// IF FORCE_SM2
		UsePass "Hidden/Toony Colors Pro 2/Outline Only (Shader Model 2)/OUTLINE_BLENDING"
		/// ELSE
		UsePass "Hidden/Toony Colors Pro 2/Outline Only/OUTLINE_BLENDING"
		///
	///
  /// ELSE

		//Outline
		Pass
		{
			Cull Front
			Offset [_Offset1],[_Offset2]

	/// IF OUTLINE_BLENDING
			Tags { "LightMode"="ForwardBase" "Queue"="Transparent" "IgnoreProjectors"="True" "RenderType"="Transparent" }
			Blend [_SrcBlendOutline] [_DstBlendOutline]
	/// ELSE
			Tags { "LightMode"="ForwardBase" "IgnoreProjectors"="True" }
	///
	/// IF OUTLINE_BEHIND_STENCIL

			Stencil
			{
				Ref [_StencilRef]
				Comp NotEqual
				Pass Keep
			}
	///

			CGPROGRAM

			#pragma vertex TCP2_Outline_Vert
			#pragma fragment TCP2_Outline_Frag

			#pragma multi_compile TCP2_NONE TCP2_ZSMOOTH_ON
			#pragma multi_compile TCP2_NONE TCP2_OUTLINE_CONST_SIZE
			#pragma multi_compile TCP2_NONE TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV2_AS_NORMALS
	/// IF !FORCE_SM2
			#pragma multi_compile TCP2_NONE TCP2_OUTLINE_TEXTURED			
	///
			#pragma multi_compile_instancing

			#pragma multi_compile EXCLUDE_TCP2_MAIN_PASS

			#pragma target @%SHADER_TARGET%@

			ENDCG
		}
  ///
///
/// IF (OUTLINE || OUTLINE_BLENDING) && OUTLINE_BEHIND_DEPTH && OUTLINE_DEPTH

		//Outline - Depth Pass Only
		Pass
		{
			Name "OUTLINE_DEPTH"

			Cull Off
			Offset [_Offset1],[_Offset2]
			Tags { "LightMode"="ForwardBase" }

			//Write to Depth Buffer only
			ColorMask 0
			ZWrite On

			CGPROGRAM
	/// IF LEGACY_OUTLINE

			#include "UnityCG.cginc"
			#include "@%INCLUDE_PATH%@/TCP2_Outline_Include.cginc"
	///

			#pragma vertex TCP2_Outline_Vert
			#pragma fragment TCP2_Outline_Frag

			#pragma multi_compile TCP2_NONE TCP2_ZSMOOTH_ON
			#pragma multi_compile TCP2_NONE TCP2_OUTLINE_CONST_SIZE
			#pragma multi_compile TCP2_NONE TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV2_AS_NORMALS
			//#pragma multi_compile TCP2_NONE TCP2_OUTLINE_TEXTURED		//Not needed for depth
			#pragma multi_compile_instancing

			#pragma multi_compile EXCLUDE_TCP2_MAIN_PASS

			#pragma target @%SHADER_TARGET%@

			ENDCG
		}
///
/// IF !NO_SHADOW && ((ALPHA && DITHERED_SHADOWS) || ((OUTLINE || OUTLINE_BLENDING) && OUTLINE_SHADOWCASTER))

		//Shadow Caster (for shadows and depth texture)
		Pass
		{
			Name "ShadowCaster"
			Tags { "LightMode" = "ShadowCaster" }

			CGPROGRAM

			#include "UnityCG.cginc"
			#pragma vertex vertShadowCaster
			#pragma fragment fragShadowCaster
			#pragma multi_compile_shadowcaster
			#pragma multi_compile_instancing

	/// IF ((OUTLINE || OUTLINE_BLENDING) && OUTLINE_SHADOWCASTER)
			#pragma multi_compile TCP2_NONE TCP2_ZSMOOTH_ON
			#pragma multi_compile TCP2_NONE TCP2_OUTLINE_CONST_SIZE
			#pragma multi_compile TCP2_NONE TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV2_AS_NORMALS
	///

			#pragma multi_compile EXCLUDE_TCP2_MAIN_PASS

			half4		_Color;
			half		_Cutoff;
			sampler2D	_MainTex;
			float4		_MainTex_ST;
	/// IF (ALPHA && DITHERED_SHADOWS)
			sampler3D	_DitherMaskLOD;
	///

			struct VertexInput
			{
				float4 vertex	: POSITION;
				float3 normal	: NORMAL;
				float2 uv0		: TEXCOORD0;
	/// IF (OUTLINE || OUTLINE_BLENDING) && OUTLINE_SHADOWCASTER
		/// IF !OUTLINE_VCOLOR_WIDTH && !VERTEXMOTION
			#if TCP2_COLORS_AS_NORMALS
		///
				float4 color : COLOR;
		/// IF !OUTLINE_VCOLOR_WIDTH && !VERTEXMOTION
			#endif
		///
		#if TCP2_UV2_AS_NORMALS
				float2 uv2 : TEXCOORD1;
		#endif
		/// IF !VERTEXMOTION_NORMAL
		#if TCP2_TANGENT_AS_NORMALS
		///
				float4 tangent : TANGENT;
		/// IF !VERTEXMOTION_NORMAL
		#endif
		///
	/// ELIF VERTEXMOTION
				float4 color : COLOR;
		/// IF VERTEXMOTION_NORMAL
				float4 tangent : TANGENT;
		///
	///
		#if UNITY_VERSION >= 550
				UNITY_VERTEX_INPUT_INSTANCE_ID
		#endif
			};

			struct VertexOutputShadowCaster
			{
				V2F_SHADOW_CASTER_NOPOS
	/// IF (ALPHA && DITHERED_SHADOWS)
				float2 tex : TEXCOORD1;
	///
			};

			void vertShadowCaster(VertexInput v, out VertexOutputShadowCaster o, out float4 opos : SV_POSITION)
			{
	/// IF VERTEXMOTION
				//VertExmotion
		/// IF VERTEXMOTION_SIMPLE
				v.vertex = VertExmotion(v.vertex, v.color);
		/// ELIF VERTEXMOTION_NORMAL
				v.vertex = VertExmotion(v.vertex, v.color, v.normal, v.tangent);
		///

	///
	/// IF (OUTLINE || OUTLINE_BLENDING) && OUTLINE_SHADOWCASTER
				//Outline: make sure that the mesh shadow/depth texture includes the extruded vertices
		#if UNITY_VERSION >= 550
				//GPU instancing support
				UNITY_SETUP_INSTANCE_ID(v);
		#endif
	  /// IF OUTLINE_FAKE_RIM_DIRLIGHT

				float3 objSpaceLight = mul(unity_WorldToObject, _WorldSpaceLightPos0).xyz;
		#ifdef TCP2_OUTLINE_CONST_SIZE
				//Camera-independent outline size
				float dist = distance(_WorldSpaceCameraPos, mul(unity_ObjectToWorld, v.vertex));
				v.vertex.xyz += objSpaceLight.xyz * 0.01 * _Outline * dist;
		#else
				v.vertex.xyz += objSpaceLight.xyz * 0.01 * _Outline;
		#endif
	  /// ELIF OUTLINE_FAKE_RIM
				v.vertex += _OutlineOffset;
	  ///

		#ifdef TCP2_COLORS_AS_NORMALS
				//Vertex Color for Normals
				float3 normal = (v.color.xyz*2) - 1;
		#elif TCP2_TANGENT_AS_NORMALS
				//Tangent for Normals
				float3 normal = v.tangent.xyz;
		#elif TCP2_UV2_AS_NORMALS
				//UV2 for Normals
				float3 n;
				//unpack uv2
				v.uv2.x = v.uv2.x * 255.0/16.0;
				n.x = floor(v.uv2.x) / 15.0;
				n.y = frac(v.uv2.x) * 16.0 / 15.0;
				//get z
				n.z = v.uv2.y;
				//transform
				n = n*2 - 1;
				float3 normal = n;
		#else
				float3 normal = v.normal;
		#endif

		#if TCP2_ZSMOOTH_ON
				//Correct Z artefacts
				normal = UnityObjectToViewPos(normal);
				normal.z = -_ZSmooth;
		#endif

	  /// IF !OUTLINE_FAKE_RIM_DIRLIGHT
		#ifdef TCP2_OUTLINE_CONST_SIZE
				//Camera-independent outline size
				float dist = distance(_WorldSpaceCameraPos, mul(unity_ObjectToWorld, v.vertex));
				#define SIZE	dist
		#else
				#define SIZE	1.0
		#endif
	  /// ELSE
				#define SIZE	0.0
	  ///

		#if TCP2_ZSMOOTH_ON
				v.vertex += float4(normalize(normal),0) * OUTLINE_WIDTH * 0.01 * SIZE;
		#else
				v.vertex += float4(normal,0) * OUTLINE_WIDTH * 0.01 * SIZE;
		#endif
	///
				
				TRANSFER_SHADOW_CASTER_NOPOS(o,opos)
	/// IF (ALPHA && DITHERED_SHADOWS)
				o.tex = TRANSFORM_TEX(v.uv0, _MainTex);
	///
			}

			half4 fragShadowCaster(VertexOutputShadowCaster i, UNITY_VPOS_TYPE vpos : VPOS) : SV_Target
			{
	/// IF (ALPHA && DITHERED_SHADOWS)
		/// IF ALPHA_NO_MAINTEX && ALPHA_NO_COLOR
				half alpha = 1;
		/// ELIF ALPHA_NO_MAINTEX
				half alpha = _Color.a;
		/// ELIF ALPHA_NO_COLOR
				half alpha = tex2D(_MainTex, i.tex).a;
		/// ELSE
				half alpha = tex2D(_MainTex, i.tex).a * _Color.a;
		///
				// Use dither mask for alpha blended shadows, based on pixel position xy
				// and alpha level. Our dither texture is 4x4x16.
				half alphaRef = tex3D(_DitherMaskLOD, float3(vpos.xy*0.25,alpha*0.9375)).a;
				clip (alphaRef - 0.01);

	///
				SHADOW_CASTER_FRAGMENT(i)
			}

			ENDCG
		}
///
	}

	CGINCLUDE

	#if !defined(EXCLUDE_TCP2_MAIN_PASS)
		#include "Lighting.cginc"

		//================================================================================================================================
		// STRUCT

		struct SurfaceOutputStandardTCP2
		{
			fixed3 Albedo;      // base (diffuse or specular) color
			fixed3 Normal;      // tangent space normal, if written
			half3 Emission;

		/// IF PBS_SPECULAR
			fixed3 Specular;    // specular color
		/// ELSE
			half Metallic;      // 0=non-metal, 1=metal
		///

			//Smoothness is the user facing name, it should be perceptual smoothness but user should not have to deal with it.
			// Everywhere in the code you meet smoothness it is perceptual smoothness
			half Smoothness;    // 0=rough, 1=smooth
			half Occlusion;     // occlusion (default 1)
			fixed Alpha;        // alpha for transparencies

			fixed atten;
/// IF SHADOW_COLOR_TEX
			fixed3 ShadowColorTex;
///
/// IF TEXTURED_THRESHOLD
			float2 texThresholdTexcoords;
///
/// IF USE_SCREENSPACE_COORDS_FRAGMENT
			float4 screenCoords;
///
/// IF (SKETCH || SKETCH_GRADIENT) && SKETCH_VERTEX
			half2 sketchUV;
///
/// IF SUBSURFACE_SCATTERING && SS_MASK
			fixed SubsurfaceMask;
///
/// IF USE_VFACE
			fixed vface;
///
		};

		//================================================================================================================================
		// VARIABLES

		sampler2D _MainTex;
/// IF NOTILE_TEXTURES
		sampler2D _NoTileNoiseTex;
		float4 _NoTileNoiseTex_TexelSize;
///
		fixed4 _Color;
		half _Cutoff;
		half _Glossiness;
		half _GlossMapScale;
/// IF PBS_SPECULAR
	/// IF SPEC_METALLIC_GLOSS_MAP
		sampler2D _SpecGlossMap;
		/// IF TEXTURE_BLENDING && TEXBLEND_SPEC_METALLIC
			/// IF BLEND_TEX1
		sampler2D _SpecGlossMap1;
			///
			/// IF BLEND_TEX2
		sampler2D _SpecGlossMap2;
			///
			/// IF BLEND_TEX3
		sampler2D _SpecGlossMap3;
			///
			/// IF BLEND_TEX4
		sampler2D _SpecGlossMap4;
			///
		///
	///
/// ELSE
		half _Metallic;
	/// IF SPEC_METALLIC_GLOSS_MAP
		sampler2D _MetallicGlossMap;
		/// IF TEXTURE_BLENDING && TEXBLEND_SPEC_METALLIC
			/// IF BLEND_TEX1
		sampler2D _MetallicGlossMap1;
			///
			/// IF BLEND_TEX2
		sampler2D _MetallicGlossMap2;
			///
			/// IF BLEND_TEX3
		sampler2D _MetallicGlossMap3;
			///
			/// IF BLEND_TEX4
		sampler2D _MetallicGlossMap4;
			///
		///
	///
///
/// IF BUMP
		half _BumpScale;
		sampler2D _BumpMap;
	/// IF TEXTURE_BLENDING && TEXBLEND_BUMP
		/// IF BLEND_TEX1
		sampler2D _BumpMap1;
		///
		/// IF BLEND_TEX2
		sampler2D _BumpMap2;
		///
		/// IF BLEND_TEX3
		sampler2D _BumpMap3;
		///
		/// IF BLEND_TEX4
		sampler2D _BumpMap4;
		///
	///
	/// IF PARALLAX
		sampler2D _ParallaxMap;
		half _Parallax;
	///
///
/// IF DETAIL_TEX
		sampler2D _DetailAlbedoMap;
	/// IF BUMP && DETAIL_BUMP
		half _DetailNormalMapScale;
		sampler2D _DetailNormalMap;
	///
	/// IF DETAIL_MASK
		sampler2D _DetailMask;
	///
///
/// IF EMISSION
		half4 _EmissionColor;
		sampler2D _EmissionMap;
///
/// IF OCCLUSION
		half _OcclusionStrength;
		sampler2D _OcclusionMap;
///

		//-------------------------------------------------------------------------------------
		//TCP2 Params

		fixed4 _HColor;
		fixed4 _SColor;
/// IF COLOR_MULTIPLIERS
		fixed _HighlightMultiplier;
		fixed _ShadowMultiplier;
///
/// IF SHADOW_COLOR_TEX
		sampler2D _STexture;
///
/// IF COLORMASK
		fixed4 _MaskedColor;
		half _ColorMaskStrength;
///
		sampler2D _Ramp;
		fixed _RampThreshold;
		fixed _RampSmooth;
		fixed _RampSmoothAdd;
/// IF MASK1
		sampler2D _Mask1;
///
/// IF MASK2
		sampler2D _Mask2;
///
/// IF MASK3
		sampler2D _Mask3;
///
/// IF TEXTURE_BLENDING
	/// IF TEXBLEND_NORMALIZE
		float _BlendContrast;
	///
	/// IF TEXBLEND_HEIGHT
		float4 _VColorBlendSmooth;
		float4 _VColorBlendOffset;
	///
	/// IF BLEND_TEX1
		sampler2D _BlendTex1;
		float4 _BlendTex1_ST;
	///
	/// IF BLEND_TEX2
		sampler2D _BlendTex2;
		float4 _BlendTex2_ST;
	///
	/// IF BLEND_TEX3
		sampler2D _BlendTex3;
		float4 _BlendTex3_ST;
	///
	/// IF BLEND_TEX4
		sampler2D _BlendTex4;
		float4 _BlendTex4_ST;
	///
	/// IF TEXBLEND_UNITY_SPLATMAP
			sampler2D _Control;
	/// ELIF TEXBLEND_MAP
			sampler2D _TexBlendMap;
	///
///
/// IF HSV_CONTROLS
		float _HSV_H;
		float _HSV_S;
		float _HSV_V;
///
/// IF SUBSURFACE_SCATTERING
		half _SSDistortion;
		half _SSPower;
		half _SSScale;
	/// IF SUBSURFACE_COLOR
		fixed4 _SSColor;
	///
	/// IF SUBSURFACE_AMB_COLOR
		fixed4 _SSAmbColor;
	///
///
/// IF SPECULAR_STYLIZED
		fixed _SpecSmooth;
		fixed _SpecBlend;
///
/// IF FRESNEL_STYLIZED
		fixed _RimStrength;
		fixed _RimMin;
		fixed _RimMax;
///
/// IF WRAP_CUSTOM
		fixed _WrapValue;
///
/// IF DIFFUSE_TINT
			fixed4 _DiffTint;
///
/// IF TEXTURED_THRESHOLD
		sampler2D _ThresholdTex;
	/// IF !(TEXTURED_THRESHOLD_UV0 || TEXTURED_THRESHOLD_UV1)
		fixed _ThresholdScale;
	///
		fixed _ThresholdStrength;
///
/// IF SKETCH || SKETCH_GRADIENT
		sampler2D _SketchTex;
		float4 _SketchTex_ST;
	/// IF SKETCH
		fixed _SketchStrength;
	/// ELIF SKETCH_GRADIENT
		fixed4 _SketchColor;
		fixed _SketchHalftoneMin;
		fixed _SketchHalftoneMax;
	///
	/// IF SKETCH_ANIM
		fixed _SketchSpeed;
	///
///

		//================================================================================================================================
		// LIGHTING / BRDF

		//-------------------------------------------------------------------------------------
		// TCP2 Tools

		inline half WrapRampNL(half nl, fixed threshold, fixed smoothness)
		{
/// IF WRAP_CUSTOM
			//nl = saturate((nl + _WrapValue)/((1+_WrapValue)*(1+_WrapValue)));	//energy-conservative wrapped diffuse
			nl = saturate((nl + _WrapValue) / (1 + _WrapValue));
/// ELIF WRAPPED_DIFFUSE
			nl = saturate(nl * 0.5 + 0.5);		//energy-conservative wrapped diffuse
/// ELSE
			nl = saturate(nl);
///
/// IF TEXTURE_RAMP
			nl = tex2D(_Ramp, nl.xx).r;
/// ELSE
			nl = smoothstep(threshold - smoothness*0.5, threshold + smoothness*0.5, nl);
///
			return nl;
		}
/// IF SPECULAR_STYLIZED

		inline half StylizedSpecular(half specularTerm, fixed specSmoothness)
		{
			return smoothstep(specSmoothness*0.5, 0.5 + specSmoothness*0.5, specularTerm);
		}
///
/// IF FRESNEL_STYLIZED

		inline half3 StylizedFresnel(half nv, half roughness, UnityLight light, half3 normal, fixed rimMin, fixed rimMax, fixed rimStrength)
		{
			half rim = 1-nv;
			rim = smoothstep(rimMin, rimMax, rim) * rimStrength * saturate(1.33-roughness);
			return rim * saturate(dot(normal, light.dir)) * light.color;
		}
///
/// IF HSV_CONTROLS

		// HSV HELPERS
		// source: http://lolengine.net/blog/2013/07/27/rgb-to-hsv-in-glsl
		float3 rgb2hsv(float3 c)
		{
			float4 K = float4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
			float4 p = lerp(float4(c.bg, K.wz), float4(c.gb, K.xy), step(c.b, c.g));
			float4 q = lerp(float4(p.xyw, c.r), float4(c.r, p.yzx), step(p.x, c.r));

			float d = q.x - min(q.w, q.y);
			float e = 1.0e-10;
			return float3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
		}

		float3 hsv2rgb(float3 c)
		{
			c.g = max(c.g, 0.0); //make sure that saturation value is positive
			float4 K = float4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
			float3 p = abs(frac(c.xxx + K.xyz) * 6.0 - K.www);
			return c.z * lerp(K.xxx, saturate(p - K.xxx), c.y);
		}
///
/// IF (SKETCH || SKETCH_GRADIENT) && !NO_SKETCH_OFFSET

		//Adjust screen UVs relative to object to prevent screen door effect
		inline void ObjSpaceUVOffset(inout float2 screenUV, in float screenRatio)
		{
			// UNITY_MATRIX_P._m11 = Camera FOV
			float4 objPos = float4(-UNITY_MATRIX_T_MV[3].x * screenRatio * UNITY_MATRIX_P._m11, -UNITY_MATRIX_T_MV[3].y * UNITY_MATRIX_P._m11, UNITY_MATRIX_T_MV[3].z, UNITY_MATRIX_T_MV[3].w);

			float offsetFactorX = 0.5;
			float offsetFactorY = offsetFactorX * screenRatio;
		/// IF !SKETCH_VERTEX
			offsetFactorX *= _SketchTex_ST.x;
			offsetFactorY *= _SketchTex_ST.y;
		///

			if (unity_OrthoParams.w < 1)	//don't scale with orthographic camera
			{
				//adjust uv scale
				screenUV -= float2(offsetFactorX, offsetFactorY);
				screenUV *= objPos.z;	//scale with cam distance
				screenUV += float2(offsetFactorX, offsetFactorY);

				// sign(UNITY_MATRIX_P[1].y) is different in Scene and Game views
				screenUV.x -= objPos.x * offsetFactorX * sign(UNITY_MATRIX_P[1].y);
				screenUV.y -= objPos.y * offsetFactorY * sign(UNITY_MATRIX_P[1].y);
			}
			else
			{
				// sign(UNITY_MATRIX_P[1].y) is different in Scene and Game views
				screenUV.x += objPos.x * offsetFactorX * sign(UNITY_MATRIX_P[1].y);
				screenUV.y += objPos.y * offsetFactorY * sign(UNITY_MATRIX_P[1].y);
			}
		}
///

		//-------------------------------------------------------------------------------------
		// Standard Shader inputs

/// IF PBS_SPECULAR

		half4 SpecularGloss(float mainTexAlpha, fixed4 specGlossMap)
		{
			half4 sg;
	/// IF SPEC_METALLIC_GLOSS_MAP
		/// IF SMOOTHNESS_ALBEDO_ALPHA
			sg.rgb = specGlossMap.rgb;
			sg.a = mainTexAlpha;
		/// ELSE
			sg = specGlossMap;
			///
			sg.a *= _GlossMapScale;
	/// ELSE
			sg.rgb = _SpecColor.rgb;
		/// IF SMOOTHNESS_ALBEDO_ALPHA
			sg.a = mainTexAlpha * _Glossiness;
		/// ELSE
			sg.a = _Glossiness;
		///
	///
			return sg;
		}
/// ELSE

		half2 MetallicGloss(float mainTexAlpha, fixed4 metalGlossMap)
		{
			half2 mg;
	/// IF SPEC_METALLIC_GLOSS_MAP
		/// IF SMOOTHNESS_ALBEDO_ALPHA
			mg.r = metalGlossMap.r;
			mg.g = mainTexAlpha;
		/// ELSE
			mg = metalGlossMap.ra;
		///
			mg.g *= _GlossMapScale;
	/// ELSE
			mg.r = _Metallic;
		/// IF SMOOTHNESS_ALBEDO_ALPHA
			mg.g = mainTexAlpha * _Glossiness;
		/// ELSE
			mg.g = _Glossiness;
		///
	///
			return mg;
		}
///

		//-------------------------------------------------------------------------------------

		// Note: BRDF entry points use oneMinusRoughness (aka "smoothness") and oneMinusReflectivity for optimization
		// purposes, mostly for DX9 SM2.0 level. Most of the math is being done on these (1-x) values, and that saves
		// a few precious ALU slots.

		// Main Physically Based BRDF
		// Derived from Disney work and based on Torrance-Sparrow micro-facet model
		//
		//   BRDF = kD / pi + kS * (D * V * F) / 4
		//   I = BRDF * NdotL
		//
		// * NDF (depending on UNITY_BRDF_GGX):
		//  a) Normalized BlinnPhong
		//  b) GGX
		// * Smith for Visiblity term
		// * Schlick approximation for Fresnel
		half4 TCP2_BRDF_PBS(half3 diffColor, half3 specColor, half oneMinusReflectivity, half smoothness, half3 normal, half3 viewDir, UnityLight light, UnityIndirect gi,
			/* TCP2 */ half atten, SurfaceOutputStandardTCP2 s
/// IF TEXTURED_THRESHOLD
			,half2 texThresholdTexcoords
///
/// IF (SKETCH || SKETCH_GRADIENT) && !SKETCH_VERTEX
			,half4 screenCoords
/// ELIF (SKETCH || SKETCH_GRADIENT) && SKETCH_VERTEX
			,half2 sketchUV
///
			)
		{
			half perceptualRoughness = SmoothnessToPerceptualRoughness (smoothness);
			half3 halfDir = Unity_SafeNormalize (light.dir + viewDir);

			// NdotV should not be negative for visible pixels, but it can happen due to perspective projection and normal mapping
			// In this case normal should be modified to become valid (i.e facing camera) and not cause weird artifacts.
			// but this operation adds few ALU and users may not want it. Alternative is to simply take the abs of NdotV (less correct but works too).
			// Following define allow to control this. Set it to 0 if ALU is critical on your platform.
			// This correction is interesting for GGX with SmithJoint visibility function because artifacts are more visible in this case due to highlight edge of rough surface
			// Edit: Disable this code by default for now as it is not compatible with two sided lighting used in SpeedTree.
			#define TCP2_HANDLE_CORRECTLY_NEGATIVE_NDOTV 0 

	#if TCP2_HANDLE_CORRECTLY_NEGATIVE_NDOTV
			// The amount we shift the normal toward the view vector is defined by the dot product.
			half shiftAmount = dot(normal, viewDir);
			normal = shiftAmount < 0.0f ? normal + viewDir * (-shiftAmount + 1e-5f) : normal;
			// A re-normalization should be applied here but as the shift is small we don't do it to save ALU.
			//normal = normalize(normal);

			half nv = saturate(dot(normal, viewDir)); // TODO: this saturate should no be necessary here
	#else
			half nv = abs(dot(normal, viewDir));	// This abs allow to limit artifact
	#endif

			half nl = dot(normal, light.dir);

/// IF DIFFUSE_TINT
			half3 diffTint = saturate(_DiffTint.rgb + nl);

///
/// IF TEXTURED_THRESHOLD
	/// IF TEXTHR_MAIN
		#if !defined(UNITY_PASS_FORWARDADD)
	/// ELIF TEXTHR_OTHER
		#if defined(UNITY_PASS_FORWARDADD)
	///
	
	/// IF !(TEXTURED_THRESHOLD_UV0 || TEXTURED_THRESHOLD_UV1)
			half2 thresholdUv = texThresholdTexcoords.xy * _ThresholdScale;
	/// ELSE
			half2 thresholdUv = texThresholdTexcoords.xy;
	///
			half texThreshold = tex2D(_ThresholdTex, thresholdUv).a - 0.5;
			nl += texThreshold * _ThresholdStrength;
	/// IF TEXTHR_MAIN || TEXTHR_OTHER
		#endif
	///
///

		#if defined(UNITY_PASS_FORWARDADD)
			#define RAMP_SMOOTH _RampSmoothAdd
		#else
			#define RAMP_SMOOTH _RampSmooth
		#endif

			//TCP2 Ramp N.L
			nl = WrapRampNL(nl, _RampThreshold, RAMP_SMOOTH);

			half nh = saturate(dot(normal, halfDir));

			half lv = saturate(dot(light.dir, viewDir));
			half lh = saturate(dot(light.dir, halfDir));

			// Diffuse term
			half diffuseTerm = DisneyDiffuse(nv, nl, lh, perceptualRoughness) * nl;

			// Specular term
			// HACK: theoretically we should divide diffuseTerm by Pi and not multiply specularTerm!
			// BUT 1) that will make shader look significantly darker than Legacy ones
			// and 2) on engine side "Non-important" lights have to be divided by Pi too in cases when they are injected into ambient SH
			half roughness = PerceptualRoughnessToRoughness(perceptualRoughness);
	#if UNITY_BRDF_GGX
			half V = SmithJointGGXVisibilityTerm (nl, nv, roughness);
			half D = GGXTerm (nh, roughness);
	#else
			// Legacy
			half V = SmithBeckmannVisibilityTerm (nl, nv, roughness);
			half D = NDFBlinnPhongNormalizedTerm (nh, PerceptualRoughnessToSpecPower(perceptualRoughness));
	#endif
/// IF SPECULAR_OFF
			half specularTerm = 0.0;
/// ELSE
			half specularTerm = V*D * UNITY_PI; // Torrance-Sparrow model, Fresnel is applied later
  /// IF SPECULAR_STYLIZED
	//TCP2 Stylized Specular
			half r = sqrt(roughness)*0.85;
			r += 1e-4h;
			specularTerm = lerp(specularTerm, StylizedSpecular(specularTerm, _SpecSmooth) * (1/r), _SpecBlend);
  ///
	#ifdef UNITY_COLORSPACE_GAMMA
			specularTerm = sqrt(max(1e-4h, specularTerm));
	#endif
			// specularTerm * nl can be NaN on Metal in some cases, use max() to make sure it's a sane value
			specularTerm = max(0, specularTerm * nl);
///

			// surfaceReduction = Int D(NdotH) * NdotH * Id(NdotL>0) dH = 1/(roughness^2+1)
			half surfaceReduction;
	#ifdef UNITY_COLORSPACE_GAMMA
			surfaceReduction = 1.0-0.28*roughness*perceptualRoughness;		// 1-0.28*x^3 as approximation for (1/(x^4+1))^(1/2.2) on the domain [0;1]
	#else
			surfaceReduction = 1.0 / (roughness*roughness + 1.0);			// fade \in [0.5;1]
	#endif

			// To provide true Lambert lighting, we need to be able to kill specular completely.
			specularTerm *= any(specColor) ? 1.0 : 0.0;

	//TCP2 Colored Highlight/Shadows
/// IF COLOR_MULTIPLIERS
			_SColor = lerp(_HColor, _SColor, _SColor.a * _ShadowMultiplier);	//Shadows intensity through alpha
			_HColor.rgb *= _HighlightMultiplier;
/// ELSE
			_SColor = lerp(_HColor, _SColor, _SColor.a);	//Shadows intensity through alpha
///
/// IF SHADOW_COLOR_TEX
			//Shadow Color Texture
	/// IF SHADOW_COLOR_TEX_MULT
			diffColor.rgb *= lerp(s.ShadowColorTex.rgb, float3(1,1,1), diffuseTerm);
	/// ELIF SHADOW_COLOR_TEX_LERP
			diffColor.rgb = lerp(s.ShadowColorTex.rgb, diffColor.rgb, diffuseTerm);
	///
///

	//light attenuation already included in light.color for point lights
	#if !defined(UNITY_PASS_FORWARDADD)
			diffuseTerm *= atten;
	#endif
			half3 diffuseTermRGB = lerp(_SColor.rgb, _HColor.rgb, diffuseTerm);
/// IF DIFFUSE_TINT
			diffuseTermRGB *= diffTint;
///
			half3 diffuseTCP2 = diffColor * (gi.diffuse + light.color * diffuseTermRGB);
			//original: diffColor * (gi.diffuse + light.color * diffuseTerm)

	//light attenuation already included in light.color for point lights
	#if !defined(UNITY_PASS_FORWARDADD)
			//TCP2: atten contribution to specular since it was removed from light calculation
			specularTerm *= atten;
	#endif

			half grazingTerm = saturate(smoothness + (1-oneMinusReflectivity));
			half3 color =	diffuseTCP2
							+ specularTerm * light.color
/// IF !FRESNEL_OFF
							* FresnelTerm (specColor, lh)
///
							+ surfaceReduction * gi.specular
/// IF FRESNEL_OFF
							* specColor;
/// ELSE
							* FresnelLerp (specColor, grazingTerm, nv);
///

/// IF FRESNEL_STYLIZED && !FRESNEL_OFF
			//TCP2 Enhanced Rim/Fresnel
			color += StylizedFresnel(nv, roughness, light, normal, _RimMin, _RimMax, _RimStrength);
///
/// IF SUBSURFACE_SCATTERING
	/// IF SS_ALL_LIGHTS
# nothing here: workaround so that point/spot lights are the default value
	/// ELIF SS_DIR_LIGHTS
		#if !defined(UNITY_PASS_FORWARDADD)
	/// ELSE
		#if defined(UNITY_PASS_FORWARDADD)
	///
			//Subsurface Scattering
			half3 ssLight = light.dir + normal * _SSDistortion;
			half ssDot = pow(saturate(dot(viewDir, -ssLight)), _SSPower) * _SSScale;
		  #if defined(UNITY_PASS_FORWARDADD)
			half ssAtten = atten * 2;
		  #else
			half ssAtten = 1;
		  #endif
	/// IF SUBSURFACE_COLOR && SUBSURFACE_AMB_COLOR
			half3 ssColor = ssAtten * ((ssDot * _SSColor.rgb) + _SSAmbColor.rgb);
	/// ELIF SUBSURFACE_COLOR
			half3 ssColor = ssAtten * (ssDot * _SSColor.rgb);
	/// ELIF SUBSURFACE_AMB_COLOR
			half3 ssColor = ssAtten * (ssDot + _SSAmbColor.rgb);
	/// ELSE
			half3 ssColor = ssAtten * ssDot;
	///
	/// IF SS_MASK
			ssColor *= s.SubsurfaceMask;
	///
	/// IF SS_COLOR_FROM_LIGHT
			ssColor.rgb *= light.color.rgb;
	///
	/// IF SS_MULTIPLICATIVE
			color.rgb *= diffColor * ssColor;
	/// ELSE
			color.rgb += diffColor * ssColor;
	///
	/// IF !SS_ALL_LIGHTS
		#endif
	///
///
/// IF SKETCH || SKETCH_GRADIENT
			//Sketch
	/// IF !SKETCH_VERTEX
			float2 screenUV = screenCoords.xy / screenCoords.w;
			screenUV = TRANSFORM_TEX(screenUV, _SketchTex);
			float screenRatio = _ScreenParams.y / _ScreenParams.x;
			screenUV.y *= screenRatio;
		/// IF !NO_SKETCH_OFFSET
			ObjSpaceUVOffset(screenUV, screenRatio);
		///
		/// IF SKETCH_ANIM
			float2 random = round(float2(_Time.z,-_Time.z) * _SketchSpeed) / _SketchSpeed;
			screenUV.xy += frac(random.xy);
		///
			float2 sketchUV = screenUV;
	///

			fixed sketch = tex2D(_SketchTex, sketchUV).a;
	/// IF SKETCH_GRADIENT
			sketch = smoothstep(sketch - 0.2, sketch, clamp(nl * atten, _SketchHalftoneMin, _SketchHalftoneMax));	//Gradient halftone
	/// ELSE
			sketch = lerp(sketch, 1, nl * atten);	//Regular sketch overlay
	///
///
/// IF SKETCH
	/// IF SKETCH_COLORBURN
			color.rgb = max((1.0 - ((1.0 - color.rgb) / sketch)), 0.0);
	/// ELSE
			color.rgb = lerp(color.rgb, sketch * color.rgb, _SketchStrength);
	///
/// ELIF SKETCH_GRADIENT
			color.rgb *= lerp(_SketchColor.rgb, fixed3(1,1,1), sketch);
///
			return half4(color, 1);
		}

		//================================================================================================================================

	#endif
	ENDCG

	FallBack "VertexLit"
	CustomEditor "TCP2_MaterialInspector_SurfacePBS_SG"
}
